from config import EQUIPMENT_TYPES, STATE_TEXT, CONTRIBUTION_TABLE, ABILITIES, COMPONENT_REPAIR_COST, TEAM_CAPACITY
import random
import math
import copy

def generate_equipments(M):
    """生成M个无人装备，随机分配类型、损坏部件、状态和能力"""
    equipments = []
    for i in range(M):
        eq_type = random.choice(EQUIPMENT_TYPES)
        damaged_components = []
        for comp in ['s1', 's2', 's3', 's4', 's5']:
            if random.random() < 0.3:  # 损坏概率
                damaged_components.append(comp)
        damage_count = len(damaged_components)
        state_key = min(damage_count, 3)
        state = STATE_TEXT[state_key]
        # 计算当前能力
        current_abilities = {ab: 0.0 for ab in ABILITIES}
        good_components = [c for c in ['s1','s2','s3','s4','s5'] if c not in damaged_components]
        for comp in good_components:
            for ab, contrib in CONTRIBUTION_TABLE[eq_type][comp].items():
                current_abilities[ab] += contrib
        repair_cost = damage_count * COMPONENT_REPAIR_COST
        equipments.append({
            'id': i + 1,
            'type': eq_type,
            'damaged_components': damaged_components,
            'state': state,
            'abilities': current_abilities,
            'repair_cost': repair_cost,
            'x': None,
            'y': None
        })
    return equipments

def calculate_repair_teams(equipments, priority_list):
    """根据优先级计算需要的维修小队数量"""
    # 计算完好状态总能力
    perfect_totals = {ab: 0.0 for ab in ABILITIES}
    for eq in equipments:
        for comp in CONTRIBUTION_TABLE[eq['type']]:
            for ab, contrib in CONTRIBUTION_TABLE[eq['type']][comp].items():
                perfect_totals[ab] += contrib
    
    # 从config导入阈值比例
    from config import ABILITY_THRESHOLD_RATIO
    
    # 设定基准值（完好状态的阈值比例）
    benchmarks = {ab: val * ABILITY_THRESHOLD_RATIO for ab, val in perfect_totals.items()}
    
    # 当前总能力
    current = {ab: sum(eq['abilities'][ab] for eq in equipments) for ab in ABILITIES}
    
    # 坏部件列表 [(eq_index, comp)]
    bad_components = []
    for idx, eq in enumerate(equipments):
        for comp in eq['damaged_components']:
            bad_components.append((idx, comp))
    
    # 记录每种类型的装备需要的维修容量（使用整数）
    repair_counts = {typ: 0 for typ in EQUIPMENT_TYPES}  # 记录每种类型修复的部件数量
    repaired_components = []
    
    # 按优先级修复部件，确保所有能力都达到阈值
    for ability in priority_list:
        while current[ability] < benchmarks[ability] and bad_components:
            # 找到对该能力提升最大的坏部件
            max_gain = 0.0  # 使用浮点数
            best_bad = None
            for bad in bad_components:
                idx, comp = bad
                eq_type = equipments[idx]['type']
                gain = CONTRIBUTION_TABLE[eq_type][comp].get(ability, 0.0)  # 使用浮点数
                if gain > max_gain:
                    max_gain = gain
                    best_bad = bad
            
            if best_bad is None or max_gain == 0.0:
                break
            
            # 修复
            idx, comp = best_bad
            eq_type = equipments[idx]['type']
            for ab, contrib in CONTRIBUTION_TABLE[eq_type][comp].items():
                current[ab] += contrib
            
            bad_components.remove(best_bad)
            repaired_components.append((idx, comp, eq_type))
            repair_counts[eq_type] += 1  # 增加修复计数（整数）
    
    # 检查是否所有能力都已达到阈值
    all_abilities_met = True
    for ability in priority_list:
        if current[ability] < benchmarks[ability]:
            all_abilities_met = False
            break
    
    # 如果所有能力都已达到阈值，则停止修复
    if all_abilities_met:
        print(f"DEBUG - 所有能力已达到阈值({ABILITY_THRESHOLD_RATIO*100:.0f}%)，停止修复")
        print(f"DEBUG - 当前能力值: {current}")
        print(f"DEBUG - 阈值: {benchmarks}")
    else:
        # 继续修复剩余部件，直到所有能力都达到阈值或没有更多部件可修复
        while bad_components:
            # 检查是否所有能力都已达到阈值
            all_abilities_met = True
            for ability in priority_list:
                if current[ability] < benchmarks[ability]:
                    all_abilities_met = False
                    break
            
            # 如果所有能力都已达到阈值，则停止修复
            if all_abilities_met:
                print(f"DEBUG - 修复过程中达到阈值({ABILITY_THRESHOLD_RATIO*100:.0f}%)，停止修复")
                print(f"DEBUG - 当前能力值: {current}")
                print(f"DEBUG - 阈值: {benchmarks}")
                break
            
            # 找到对任何能力提升最大的坏部件
            max_total_gain = 0.0
            best_bad = None
            for bad in bad_components:
                idx, comp = bad
                eq_type = equipments[idx]['type']
                total_gain = sum(CONTRIBUTION_TABLE[eq_type][comp].get(ab, 0.0) for ab in ABILITIES)
                if total_gain > max_total_gain:
                    max_total_gain = total_gain
                    best_bad = bad
            
            if best_bad is None or max_total_gain == 0.0:
                break
            
            # 修复
            idx, comp = best_bad
            eq_type = equipments[idx]['type']
            for ab, contrib in CONTRIBUTION_TABLE[eq_type][comp].items():
                current[ab] += contrib
            
            bad_components.remove(best_bad)
            repaired_components.append((idx, comp, eq_type))
            repair_counts[eq_type] += 1
    
    # 计算维修容量需求（每个部件修复成本为2）
    component_repair_cost_int = 2  # 整数版本的 COMPONENT_REPAIR_COST
    capacity_needed = {typ: count * component_repair_cost_int for typ, count in repair_counts.items()}
    
    # 计算每种类型需要的维修小队数量
    # 使用 Z1-Z5 作为键名，对应 B1-B5 的维修小队
    teams = {}
    for i, typ in enumerate(EQUIPMENT_TYPES):
        cost = capacity_needed[typ]
        teams[typ] = 0 if cost == 0 else (cost + TEAM_CAPACITY - 1) // TEAM_CAPACITY  # 向上取整的整数除法
    
    total_teams = sum(teams.values())
    total_capacity = sum(capacity_needed.values())
    
    # 创建 B1-B5 的维修小队映射，与前端显示对应
    team_types = ['B1', 'B2', 'B3', 'B4', 'B5']
    teams_by_b = {}
    for i, typ in enumerate(EQUIPMENT_TYPES):
        teams_by_b[team_types[i]] = teams[typ]
    
    # 准备细节
    equipment_details = []
    for eq in equipments:
        equipment_details.append({
            'id': eq['id'],
            'type': eq['type'],
            'damaged_components': eq['damaged_components'],
            'state': eq['state'],
            'abilities': eq['abilities'],
            'repair_cost': eq['repair_cost']
        })
    
    # print(f"DEBUG - 修复的部件数量: {repair_counts}")
    # print(f"DEBUG - 维修容量需求: {capacity_needed}")
    # print(f"DEBUG - 维修小队数量 (Z): {teams}")
    # print(f"DEBUG - 维修小队数量 (B): {teams_by_b}")
    # print(f"DEBUG - 总维修小队数量: {total_teams}")
    
    # 记录需要修复的装备ID
    repaired_equipment_ids = set()
    for idx, _, _ in repaired_components:
        repaired_equipment_ids.add(equipments[idx]['id'])
    
    result = {
        'teams': teams,  # 使用 Z1-Z5 作为键名
        'teams_by_b': teams_by_b,  # 使用 B1-B5 作为键名
        'capacity_needed': capacity_needed, 
        'total_teams': total_teams, 
        'equipment_details': equipment_details,
        'repaired_equipment_ids': list(repaired_equipment_ids)  # 需要修复的装备ID列表
    }
    
    # print(f"DEBUG - 返回结果: {result}")
    
    return result

def place_equipments_on_map(equipments, trapezoid_random_point_func):
    """将生成的无人装备放置在地图上"""
    placed_equipments = []
    equipment_set = set()
    
    for equipment in equipments:
        while True:
            pt = trapezoid_random_point_func()
            key = (round(pt[0], 4), round(pt[1], 4))
            if key not in equipment_set:
                new_equipment = equipment.copy()
                new_equipment['x'] = pt[0]
                new_equipment['y'] = pt[1]
                placed_equipments.append(new_equipment)
                equipment_set.add(key)
                break
    
    return placed_equipments, equipment_set 

"""
def simulate_repair_timeline(equipments, priority_list, paths, component_repair_time=1/3):
    from copy import deepcopy
    from collections import defaultdict
    # Group paths by camp and team (assuming one team per type per camp for simplicity; adjust if needed)
    team_paths = defaultdict(list)
    for path in paths:
        # Assume camp_id and some team_id; since not present, perhaps group by camp and type
        # For now, assume each path is for a separate 'team' but that's not accurate.
        # Actually, to properly simulate, need team-specific sequences from optimizer.
        # But since optimizer has per team eqs, perhaps this function needs team_sequences input.
        # Wait, this is tricky; perhaps move this simulation to optimizer after allocation.
""" 