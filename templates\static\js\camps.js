// 显示集中营详细信息
function showCampDetail(camps) {
    const area = document.getElementById('campDetailArea');
    const table = document.getElementById('campDetailTable');
    table.innerHTML = '';
    camps.forEach((c, i) => {
        table.innerHTML += `<tr>
            <td>${c.id}</td>
            <td>${c.name}</td>
            <td>(${c.x.toFixed(2)}, ${c.y.toFixed(2)})</td>
        </tr>`;
    });
    area.style.display = 'block';
}

// 显示集中营分配明细
function showCampAssignDetail(assigns) {
    const area = document.getElementById('campAssignArea');
    const table = document.getElementById('campAssignTable');
    table.innerHTML = '';
    assigns.forEach((c, i) => {
        table.innerHTML += `<tr>
            <td>${c.camp_id}</td>
            <td>${c.camp_name}</td>
            <td>(${c.x.toFixed(2)}, ${c.y.toFixed(2)})</td>
            <td>${c.type1_count}</td>
            <td>${c.type2_count}</td>
            <td>${c.type3_count}</td>
        </tr>`;
    });
    area.style.display = 'block';
}

// 显示路径与时间成本
function showPathDetail(paths, totalTime) {
    const area = document.getElementById('pathDetailArea');
    const table = document.getElementById('pathDetailTable');
    const total = document.getElementById('totalTimeCost');
    const unassignedStations = document.getElementById('unassignedStations');
    const unassignedCount = document.getElementById('unassignedCount');

    table.innerHTML = '';
    let unassignedCountValue = 0;
    paths.forEach((p, i) => {
        if (!p.camp_id) { // 未分配的维修站
            unassignedCountValue++;
            table.innerHTML += `<tr>
                <td>${p.station_id||i+1}</td>
                <td>${p.station_type}</td>
                <td>${p.station_status}</td>
                <td>未分配</td>
                <td>${p.distance}</td>
                <td>${p.time}</td>
            </tr>`;
        } else {
            table.innerHTML += `<tr>
                <td>${p.station_id||i+1}</td>
                <td>${p.station_type}</td>
                <td>${p.station_status}</td>
                <td>${p.camp_id}</td>
                <td>${p.distance}</td>
                <td>${p.time}</td>
            </tr>`;
        }
    });
    total.textContent = formatTime(totalTime);
    unassignedStations.style.display = unassignedCountValue > 0 ? 'block' : 'none';
    unassignedCount.textContent = unassignedCountValue;
    area.style.display = 'block';
}

// 格式化时间
function formatTime(seconds) {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.round(seconds % 60);
    const parts = [];
    if (h > 0) parts.push(`${h}小时`);
    if (m > 0) parts.push(`${m}分钟`);
    if (s > 0 || parts.length === 0) parts.push(`${s}秒`);
    return parts.join('');
}

// 计算两点间距离
function distance(p1, p2) {
    return Math.hypot(p1.x - p2.x, p1.y - p2.y);
} 