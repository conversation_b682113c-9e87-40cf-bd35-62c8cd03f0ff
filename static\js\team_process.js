// 显示小队维修过程详情
function showTeamProcessDetail(teamProcesses) {
    const area = document.getElementById('teamProcessArea');
    const content = document.getElementById('teamProcessContent');
    
    if (!area || !content) {
        console.error('teamProcessArea 或 teamProcessContent 不存在');
        return;
    }
    
    console.log('teamProcesses 数据:', teamProcesses);  // 添加调试日志，检查数据是否到达
    
    area.style.display = 'block';  // 总是显示模块
    
    if (!teamProcesses || teamProcesses.length === 0) {
        content.innerHTML = '<p style="color: #999; text-align: center;">暂无小队维修过程数据（可能由于小队容量不足或无任务分配）</p>';
        return;
    }
    
    let html = '';
    
    teamProcesses.forEach(team => {
        // 获取小队类型名称
        const teamTypeName = team.team_type || `维修小队${team.team_id}`;
        
        html += `
            <div class="team-process-card" style="margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; padding: 15px;">
                <div class="team-header" style="background: #f8f9fa; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0;">
                    <h4 style="margin: 0; color: #333;">
                        ${teamTypeName} (${team.camp_name})
                    </h4>
                    <div style="font-size: 14px; color: #666; margin-top: 5px;">
                        总用时: ${formatTime(team.total_time)}
                    </div>
                </div>
                
                <div class="team-steps">
        `;
        
        team.steps.forEach((step, stepIndex) => {
            html += `
                <div class="step repair-step" style="margin-bottom: 10px; padding: 8px; background: #f3e5f5; border-left: 4px solid #9c27b0; border-radius: 4px;">
                    <div style="font-weight: bold; color: #7b1fa2;">
                        🔧 维修步骤 ${stepIndex + 1}
                    </div>
                    <div style="margin-top: 5px;">
                        <strong>移动时间:</strong> ${formatTime(step.move_time)}
                    </div>
                    <div>
                        <strong>维修时间:</strong> ${formatTime(step.repair_time)}
                    </div>
                    <div>
                        <strong>累计时间:</strong> ${formatTime(step.total_time)}
                    </div>
                    <div>
                        <strong>装备:</strong> 装备${step.equipment_id} (${step.equipment_type}) - ${step.equipment_state}
                    </div>
                    <div>
                        <strong>维修部件:</strong> ${step.components ? step.components.join(', ') : '未知部件'}
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    });
    
    content.innerHTML = html;
}

// 格式化时间（小时）
function formatTime(hours) {
    // 先确保小数点后只保留两位，避免浮点数精度问题
    hours = Math.round(hours * 100) / 100;
    
    if (hours < 1) {
        // 对于不足1小时的时间，直接转换为分钟并四舍五入
        const minutes = Math.floor(hours * 60);  // 使用floor而不是round，避免向上舍入导致的累加误差
        return `${minutes}分钟`;
    } else {
        const h = Math.floor(hours);
        // 对于分钟部分，使用floor而不是round，确保一致性
        const m = Math.floor((hours - h) * 60);
        if (m > 0) {
            return `${h}小时${m}分钟`;
        } else {
            return `${h}小时`;
        }
    }
} 