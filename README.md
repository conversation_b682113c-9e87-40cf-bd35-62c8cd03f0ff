# 维修小队计算器

这是一个基于Python Flask的维修小队计算可视化系统，用于根据维修站数量和状态计算所需的维修小队数量。

## 功能特点

- 🏢 支持3种维修站类型：A、B、C
- 🔧 支持3种维修站状态：轻微受损(2点)、中等受损(4点)、严重受损(6点)
- 👥 3种维修小队：类型1负责A站、类型2负责B站、类型3负责C站
- 💪 每个维修小队固定维修能力：20点
- 📊 实时计算并可视化显示结果
- 🎨 现代化美观的用户界面

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行应用
```bash
python main.py
```

### 3. 访问应用
打开浏览器访问：http://localhost:5000

## 使用说明

1. 在输入框中输入维修站数量M
2. 点击"计算维修小队"按钮
3. 系统会随机生成M个维修站（随机分配类型和状态）
4. 根据维修站情况计算所需的维修小队数量
5. 查看详细的计算结果和维修站信息

## 计算逻辑

- 维修小队1只能负责维修站A
- 维修小队2只能负责维修站B  
- 维修小队3只能负责维修站C
- 每个维修小队有20点固定维修能力
- 维修站状态决定消耗的维修能力：
  - 轻微受损：2点
  - 中等受损：4点
  - 严重受损：6点
- 所需维修小队数量 = ⌈总维修能力需求 ÷ 20⌉

## 项目结构

```
weixiu/
├── main.py              # Flask主应用
├── requirements.txt     # Python依赖
├── README.md           # 项目说明
├── 说明.txt            # 需求说明
└── templates/
    └── index.html      # 前端页面
```

## 技术栈

- **后端**: Python Flask
- **前端**: HTML5, CSS3, JavaScript
- **样式**: 现代化渐变设计，响应式布局 