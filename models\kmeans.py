import numpy as np
import random
from models.camp import generate_camps

import time

def weighted_kmeans_clustering(coordinates, weights, k, trapezoid_params, equipments, threats):
    """
    使用加权K-means聚类确定集中营的最佳位置
    
    参数:
    - coordinates: 维修站坐标列表 [(x1,y1), (x2,y2), ...]
    - weights: 维修站权重列表 [w1, w2, ...]
    - k: 集中营数量
    - trapezoid_params: 梯形地图参数
    - equipments: 完整的维修站信息
    - threats: 威胁点信息
    
    返回:
    - camps: 优化后的集中营位置列表
    """
    # 开始计时
    clustering_start_time = time.time()
    if not coordinates:
        # 如果没有维修站，直接随机生成集中营
        from models.utils import trapezoid_random_point
        station_set = {(round(s['x'], 4), round(s['y'], 4)) for s in equipments}
        threat_set = {(round(t['x'], 4), round(t['y'], 4)) for t in threats}
        return generate_camps(k, station_set, threat_set, trapezoid_random_point)
    
    # 维修站数量少于集中营数量时的优化处理
    actual_k = k
    if len(coordinates) < k:
        # 计算需要生成的虚拟点数量
        needed_virtual_points = k - len(coordinates)
        virtual_coords = []

        # 为每个维修站创建虚拟点，确保集中营会分布在维修站周围
        points_per_station = max(1, needed_virtual_points // len(coordinates))
        remaining_points = needed_virtual_points % len(coordinates)

        for i, coord in enumerate(coordinates):
            # 为当前维修站生成虚拟点
            num_points = points_per_station + (1 if i < remaining_points else 0)
            for j in range(num_points):
                # 随机偏移，但保持在维修站附近
                offset_x = random.uniform(-1.0, 1.0)
                offset_y = random.uniform(-1.0, 1.0)
                virtual_coords.append((coord[0] + offset_x, coord[1] + offset_y))

        # 将原始坐标和虚拟坐标合并
        combined_coords = coordinates + virtual_coords
        # 对应的权重，虚拟点权重较低
        combined_weights = weights + [0.1] * len(virtual_coords)

        # 使用合并后的坐标进行K-means聚类
        coordinates = combined_coords
        weights = combined_weights
    
    # 转换成numpy数组便于计算
    X = np.array(coordinates)
    W = np.array(weights)
    
    # 随机初始化集中营点位置（从维修站中随机选择）
    if len(X) >= actual_k:
        indices = np.random.choice(len(X), size=actual_k, replace=False)
    else:
        # 如果维修站数量少于集中营数量，允许重复选择
        indices = np.random.choice(len(X), size=actual_k, replace=True)
    centers = X[indices].copy()
    
    # 记录上一次迭代的分配结果
    last_labels = None
    
    # 开始迭代
    max_iter = 20
    for _ in range(max_iter):
        # 计算每个维修站到每个集中营的距离
        distances = np.zeros((len(X), actual_k))
        for i in range(actual_k):
            distances[:, i] = np.sqrt(np.sum((X - centers[i])**2, axis=1))
        
        # 为每个维修站分配最近的集中营
        labels = np.argmin(distances, axis=1)
        
        # 如果分配结果与上一次相同，则已收敛
        if last_labels is not None and np.all(labels == last_labels):
            break
        
        last_labels = labels.copy()
        
        # 更新集中营位置（加权平均）
        for i in range(actual_k):
            mask = (labels == i)
            if np.any(mask):
                centers[i] = np.average(X[mask], axis=0, weights=W[mask])
    
    # 处理边界情况：防止分配到空集中营
    for i in range(actual_k):
        if np.sum(labels == i) == 0:
            # 如果某个集中营没有分配到维修站，重新随机初始化该集中营位置
            centers[i] = X[np.random.choice(len(X))].copy()
    
    # 检查集中营位置是否在地图边界内
    top, bottom, height = trapezoid_params['top'], trapezoid_params['bottom'], trapezoid_params['height']
    for i in range(actual_k):
        x, y = centers[i]
        # 计算当前y位置处的边界宽度
        current_width = top + (bottom - top) * (y / height)
        # 限制x坐标在边界内
        if y < 0:
            centers[i][1] = 0
        elif y > height:
            centers[i][1] = height
            
        half_width = current_width / 2
        if x < -half_width:
            centers[i][0] = -half_width
        elif x > half_width:
            centers[i][0] = half_width
    
    # 确保集中营位置不与维修站或威胁点重叠
    station_set = {(round(s['x'], 4), round(s['y'], 4)) for s in equipments}
    threat_set = {(round(t['x'], 4), round(t['y'], 4)) for t in threats}
    
    # 构建最终的集中营列表
    camps = []
    for i in range(actual_k):
        x, y = centers[i]
        
        # 如果当前位置与维修站或威胁点重叠，则微调位置
        key = (round(x, 4), round(y, 4))
        if key in station_set or key in threat_set:
            # 在附近寻找可用位置
            found = False
            for dx in [-0.5, 0, 0.5]:
                for dy in [-0.5, 0, 0.5]:
                    if dx == 0 and dy == 0:
                        continue
                    new_x, new_y = x + dx, y + dy
                    new_key = (round(new_x, 4), round(new_y, 4))
                    if new_key not in station_set and new_key not in threat_set:
                        x, y = new_x, new_y
                        found = True
                        break
                if found:
                    break
        
        camps.append({
            'id': i + 1,
            'name': f'集中营{str(i+1).zfill(2)}',
            'x': x,
            'y': y
        })
    
    # 计算并输出聚类时间
    clustering_end_time = time.time()
    clustering_time = clustering_end_time - clustering_start_time
    print(f"K-means聚类优化完成，耗时: {clustering_time:.4f}秒")
    
    return camps 