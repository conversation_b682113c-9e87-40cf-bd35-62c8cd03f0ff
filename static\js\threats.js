// 显示威胁点详细信息
function showThreatDetail(threats) {
    const area = document.getElementById('threatDetailArea');
    const toggleBtn = document.getElementById('threatInfoToggle');
    const detailContent = document.getElementById('threatDetailContent');
    const table = document.getElementById('threatDetailTable');
    if (table) table.innerHTML = '';
    threats.forEach((t, i) => {
        if (table) table.innerHTML += `<tr data-id="${t.id}">
            <td>${t.id}</td>
            <td>${t.name}</td>
            <td>${t.range.toFixed(1)}</td>
            <td>(${t.x.toFixed(2)}, ${t.y.toFixed(2)})</td>
            <td>
                <button class="edit-btn" onclick="editThreat(${t.id})">编辑</button>
                <button class="delete-btn" onclick="deleteThreat(${t.id})">删除</button>
            </td>
        </tr>`;
    });
    if (area) area.style.display = 'block';
    if (toggleBtn) toggleBtn.textContent = '展开详情';
    if (detailContent) detailContent.style.display = 'none';
    
    if (toggleBtn) {
        toggleBtn.onclick = function() {
            if (detailContent && detailContent.style.display === 'none') {
                detailContent.style.display = 'block';
                toggleBtn.textContent = '收起详情';
            } else {
                detailContent.style.display = 'none';
                toggleBtn.textContent = '展开详情';
            }
        };
    }
}

// 编辑威胁点
function editThreat(id) {
    const row = document.querySelector(`#threatDetailTable tr[data-id="${id}"]`);
    if (!row) return;
    
    const cells = row.cells;
    if (cells && cells[2]) { const originalRange = cells[2].innerHTML; }
    if (cells && cells[3]) { const originalCoords = cells[3].innerHTML; }
    
    // 创建范围编辑表单
    if (cells && cells[2]) cells[2].innerHTML = `
        <input type="number" class="coordinate-input" value="${rangeValue}" step="0.1" min="0.5" max="2.0" id="edit-threat-range-${id}">
    `;
    
    // 创建坐标编辑表单
    if (cells && cells[3]) cells[3].innerHTML = `
        <input type="number" class="coordinate-input" value="${coords[0]}" step="0.01" min="-25" max="25" id="edit-threat-x-${id}">
        <input type="number" class="coordinate-input" value="${coords[1]}" step="0.01" min="0" max="32" id="edit-threat-y-${id}">
    `;
    
    // 替换编辑按钮
    if (cells && cells[4]) cells[4].innerHTML = `
        <button class="save-btn" onclick="saveThreatEdit(${id}, '${originalRange}', '${originalCoords}')">保存</button>
        <button class="cancel-btn" onclick="cancelThreatEdit(${id}, '${originalRange}', '${originalCoords}')">取消</button>
    `;
}

// 保存威胁点编辑
function saveThreatEdit(id, originalRange, originalCoords) {
    const rangeInput = document.getElementById(`edit-threat-range-${id}`);
    const xInput = document.getElementById(`edit-threat-x-${id}`);
    const yInput = document.getElementById(`edit-threat-y-${id}`);
    
    if (!rangeInput || !xInput || !yInput) return;
    
    const newRange = parseFloat(rangeInput.value);
    const newX = parseFloat(xInput.value);
    const newY = parseFloat(yInput.value);
    
    // 验证范围输入
    if (isNaN(newRange) || newRange < 0.5 || newRange > 2.0) {
        alert('请输入0.5到2.0之间的有效范围值');
        return;
    }
    
    // 验证坐标输入
    if (isNaN(newX) || isNaN(newY)) {
        alert('请输入有效的坐标值');
        return;
    }
    
    // 验证坐标是否在梯形区域内
    const top = 12.0;
    const bottom = 50.0;
    const height = 32.0;
    const width = top + (bottom - top) * (newY / height);
    
    if (newY < 0 || newY > height || Math.abs(newX) > width/2) {
        alert('坐标超出地图范围');
        return;
    }
    
    // 更新数据
    const threat = window.lastMapData.threats.find(t => t.id === id);
    if (threat) {
        threat.range = newRange;
        threat.x = newX;
        threat.y = newY;
        
        // 更新表格显示
        const row = document.querySelector(`#threatDetailTable tr[data-id="${id}"]`);
        if (row && row.cells[2]) row.cells[2].innerHTML = `${newRange.toFixed(1)}`;
        if (row && row.cells[3]) row.cells[3].innerHTML = `(${newX.toFixed(2)}, ${newY.toFixed(2)})`;
        if (row && row.cells[4]) row.cells[4].innerHTML = `
            <button class="edit-btn" onclick="editThreat(${id})">编辑</button>
            <button class="delete-btn" onclick="deleteThreat(${id})">删除</button>
        `;
        
        // 重新计算威胁热力图
        recalculateThreatHeatmap();
        
        // 重绘地图
        drawMainMap(window.lastMapData);
    }
}

// 取消威胁点编辑
function cancelThreatEdit(id, originalRange, originalCoords) {
    const row = document.querySelector(`#threatDetailTable tr[data-id="${id}"]`);
    if (!row) return;
    
    // 恢复原始内容
    if (row && row.cells[2]) row.cells[2].innerHTML = originalRange;
    if (row && row.cells[3]) row.cells[3].innerHTML = originalCoords;
    if (row && row.cells[4]) row.cells[4].innerHTML = `
        <button class="edit-btn" onclick="editThreat(${id})">编辑</button>
        <button class="delete-btn" onclick="deleteThreat(${id})">删除</button>
    `;
}

// 删除威胁点
function deleteThreat(id) {
    if (!confirm(`确定要删除威胁点 #${id} 吗？`)) return;
    
    // 从数据中删除
    const threatIndex = window.lastMapData.threats.findIndex(t => t.id === id);
    if (threatIndex !== -1) {
        window.lastMapData.threats.splice(threatIndex, 1);
        
        // 从表格中删除
        const row = document.querySelector(`#threatDetailTable tr[data-id="${id}"]`);
        if (row) row.remove();
        
        // 重新计算威胁热力图
        recalculateThreatHeatmap();
        
        // 重绘地图
        drawMainMap(window.lastMapData);
    }
} 