<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维修小队与地图可视化</title>
    <link rel="stylesheet" href="static/css/main.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 维修小队与地图可视化</h1>
            <p>输入维修站数量M、威胁点数量K，计算维修小队并可视化地图</p>
        </div>
        <div class="content">
            <div class="input-section">
                <div class="input-group">
                    <label for="inputM">维修站数量 (M)</label>
                    <input type="number" id="inputM" min="10" max="1000" value="5" placeholder="请输入M">
                </div>
                <div class="input-group">
                    <label for="inputK">威胁点数量 (K)</label>
                    <input type="number" id="inputK" min="1" max="1000" value="5" placeholder="请输入K">
                </div>
                <div class="input-group">
                    <label for="inputP">集中营数量 (P)</label>
                    <input type="number" id="inputP" min="1" max="20" value="1" placeholder="请输入P">
                </div>
                <div class="input-group">
                    <label for="inputN1">预警探测能力标准值 (N1)</label>
                    <input type="number" id="inputN1" min="0" value="100" placeholder="N1标准值">
                </div>
                <div class="input-group">
                    <label for="inputN2">预警探测能力标准值 (N2)</label>
                    <input type="number" id="inputN2" min="0" value="90" placeholder="N2标准值">
                </div>
                <div class="input-group">
                    <label for="inputN3">预警探测能力标准值 (N3)</label>
                    <input type="number" id="inputN3" min="0" value="80" placeholder="N3标准值">
                </div>
                <div class="input-group">
                    <label for="inputN4">预警探测能力标准值 (N4)</label>
                    <input type="number" id="inputN4" min="0" value="70" placeholder="N4标准值">
                </div>
                <div class="input-group">
                    <label for="inputN5">预警探测能力标准值 (N5)</label>
                    <input type="number" id="inputN5" min="0" value="60" placeholder="N5标准值">
                </div>
                <button id="mainBtn" style="margin-top:24px;">计算并生成地图</button>
            </div>
            <div class="loading" id="mainLoading" style="display:none;">正在计算与生成地图...</div>
            <div class="error" id="mainError" style="display:none;"></div>
            <div id="resultArea" style="display:none;">
                <div class="result-card">
                    <h3>📊 维修小队需求汇总</h3>
                    <div class="team-summary" id="mainTeamSummary"></div>
                </div>
            </div>
            <div class="map-section">
                <canvas id="mainMapCanvas" width="800" height="600" style="background:#f8f9fa;border-radius:10px;cursor:grab;"></canvas>
                <div class="map-legend" style="white-space:normal;line-height:2.2;max-width:800px;">
                    {% for typ in EQUIPMENT_TYPE_COLORS.keys() %}
                    <span><span class="station-triangle" style="border-bottom-color:{{ EQUIPMENT_TYPE_COLORS[typ] }};"></span>无人装备{{ typ }}</span>
                    {% endfor %}<br/>
                    {% for state, color in STATE_COLOR_MAP.items() %}
                    <span style="color:{{ color }};">● {{ state }}</span>
                    {% endfor %}<br/>
                    <span><span class="legend-dot legend-threat"></span>威胁点</span>
                    <span><span class="camp-star">★</span>集中营</span>
                    <span><span class="legend-threat-area"></span>威胁区域</span>
                </div>
            </div>
            <div id="campAssignArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>📦 集中营分配明细</h3>
                    <table class="table-align" style="width:100%;border-collapse:collapse;">
                        <tbody id="campAssignTable"></tbody>
                    </table>
                </div>
            </div>
            <div id="campDetailArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>⭐ 集中营详细信息</h3>
                    <table class="table-align" style="width:100%;border-collapse:collapse;">
                        <thead>
                            <tr>
                                <th>编号</th>
                                <th>名称</th>
                                <th>坐标 (x, y)</th>
                            </tr>
                        </thead>
                        <tbody id="campDetailTable"></tbody>
                    </table>
                </div>
            </div>
            <div id="stationDetailArea" style="margin-top:30px;display:none;">
                <div class="result-card">
                    <h3>🔍 无人装备详细信息 <button id="stationInfoToggle" class="toggle-btn">展开详情</button> <button id="recalculateBtn" class="recalculate-btn">重新计算并生成地图</button></h3>
                    <div id="stationDetailContent" style="display:none;">
                        <table class="table-align" style="width:100%;border-collapse:collapse;">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>类型</th>
                                    <th>损坏部件</th>
                                    <th>状态</th>
                                    <th>能力</th>
                                    <th>坐标 (x, y)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="stationDetailTable"></tbody>
                            <tfoot><tr><td colspan="6"></td><td><button id="addEquipmentBtn" class="add-btn">添加</button></td></tr></tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div id="threatDetailArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>🔍 威胁点详细信息 <button id="threatInfoToggle" class="toggle-btn">展开详情</button> <button id="recalculateBtn2" class="recalculate-btn">重新计算并生成地图</button></h3>
                    <div id="threatDetailContent" style="display:none;">
                        <table class="table-align" style="width:100%;border-collapse:collapse;">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>威胁点</th>
                                    <th>威胁范围 (km)</th>
                                    <th>坐标 (x, y)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="threatDetailTable"></tbody>
                            <tfoot><tr><td colspan="4"></td><td><button id="addThreatBtn" class="add-btn">添加</button></td></tr></tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div id="pathDetailArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>🚗 路径与时间成本</h3>
                    <div id="unassignedStations" class="alert alert-warning" style="margin-bottom:15px;padding:10px;background-color:#fff3cd;color:#856404;border:1px solid #ffeeba;border-radius:4px;display:none;">
                        ⚠️ 注意：有<span id="unassignedCount">0</span>个维修站未被分配，因为没有集中营拥有对应类型的维修小队。
                    </div>
                    <table class="table-align" style="width:100%;border-collapse:collapse;">
                        <thead>
                            <tr>
                                <th>无人装备编号</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>集中营编号</th>
                                <th>距离 (km)</th>
                                <th>时间 (h)</th>
                            </tr>
                        </thead>
                        <tbody id="pathDetailTable"></tbody>
                    </table>
                    <div style="margin-top:10px;font-weight:bold;">总移动时间：<span id="totalTimeCost"></span></div>
                </div>
            </div>
            <div id="nAbilityChartArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>📈 能力动态变化（N1-N5）</h3>
                    <div id="nAbilityChart" style="width:100%;height:400px;"></div>
                </div>
            </div>
            <div id="teamProcessArea" style="margin-top:20px;display:none;">
                <div class="result-card">
                    <h3>🔧 小队维修过程详情</h3>
                    <div id="teamProcessContent"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 注入维修小队类型配置 -->
    <script>
        window.TEAM_TYPES = {{ TEAM_TYPES|tojson|safe }};
        window.TEAM_TYPE_NAMES = {{ TEAM_TYPE_NAMES|tojson|safe }};
        window.STATE_COLOR_MAP = {{ STATE_COLOR_MAP|tojson|safe }};
        window.EQUIPMENT_TYPE_COLORS = {{ EQUIPMENT_TYPE_COLORS|tojson|safe }};
        window.EQUIPMENT_TYPES = {{ EQUIPMENT_TYPES|tojson|safe }};
    </script>
    <!-- 引入JavaScript文件 -->
    <script src="/static/js/map.js"></script>
    <script src="/static/js/stations.js"></script>
    <script src="/static/js/threats.js"></script>
    <script src="/static/js/camps.js"></script>
    <script src="/static/js/team_process.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html> 