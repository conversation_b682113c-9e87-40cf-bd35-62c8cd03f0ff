import random
from config import THREAT_RANGE_MIN, THREAT_RANGE_MAX, THREAT_RANGE_STEP

def generate_threats(K, equipment_set, trapezoid_random_point_func):
    """生成威胁点，不能与维修站重叠"""
    threats = []
    threat_set = set()
    
    # 生成可能的范围值列表，精度为0.1
    possible_ranges = [round(THREAT_RANGE_MIN + i * THREAT_RANGE_STEP, 1) 
                      for i in range(int((THREAT_RANGE_MAX - THREAT_RANGE_MIN) / THREAT_RANGE_STEP) + 1)]
    
    for i in range(K):
        while True:
            pt = trapezoid_random_point_func()
            key = (round(pt[0], 4), round(pt[1], 4))
            if key not in equipment_set and key not in threat_set:
                # 从可能的范围值中随机选择
                threat_range = random.choice(possible_ranges)
                threats.append({
                    'id': i+1,
                    'name': f'威胁点{str(i+1).zfill(2)}',
                    'x': pt[0],
                    'y': pt[1],
                    'range': threat_range,
                    'value': 100
                })
                threat_set.add(key)
                break
    
    return threats, threat_set

def threat_cost(x, y, threats):
    """计算某点的威胁代价"""
    from models.utils import distance
    
    cost = 1.0
    for th in threats:
        dth = distance((x, y), (th['x'], th['y']))
        if dth <= th['range']:
            threat_val = max(0, th['value'] * (1 - dth/th['range']))
            cost += threat_val/100.0
    return cost

def calculate_threat_heatmap(threats, map_bounds, grid_step=2.0):
    """计算威胁热力图"""
    from models.utils import distance
    
    grid = []
    y = 0.0
    while y <= map_bounds['height']:
        width = map_bounds['top'] + (map_bounds['bottom'] - map_bounds['top']) * (y / map_bounds['height'])
        x_min = -width/2
        x_max = width/2
        x = x_min
        while x <= x_max:
            threat_value = 0.0
            for t in threats:
                d = distance((x, y), (t['x'], t['y']))
                if d <= t['range']:
                    # 线性递减，中心100，边界0
                    threat_value += max(0, t['value'] * (1 - d/t['range']))
            # 只在有威胁的地方添加数据点
            if threat_value > 0:
                grid.append({'x': x, 'y': y, 'value': threat_value/100})  # 归一化到0~1
            x += grid_step
        y += grid_step
    
    return grid 