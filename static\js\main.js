// 确保所有 JS 文件加载完成
window.onload = function() {
    console.log("所有 JS 文件已加载完成");
    
    // 检查关键函数是否存在
    if (typeof showStationDetail !== 'function') {
        console.error("showStationDetail 函数未定义，尝试从 window 对象获取");
    }
    
    // 添加计算按钮事件
    const mainBtn = document.getElementById('mainBtn');
    if (mainBtn) {
        mainBtn.addEventListener('click', calculateAndDraw);
    }
    
    // 添加重新计算按钮事件
    const recalculateBtn = document.getElementById('recalculateBtn');
    if (recalculateBtn) {
        recalculateBtn.addEventListener('click', recalculateAndRedraw);
    }
    
    const recalculateBtn2 = document.getElementById('recalculateBtn2');
    if (recalculateBtn2) {
        recalculateBtn2.addEventListener('click', recalculateAndRedraw);
    }
};

// 计算并绘制
async function calculateAndDraw() {
    const M = document.getElementById('inputM').value;
    const K = document.getElementById('inputK').value;
    const P = document.getElementById('inputP').value;
    const btn = document.getElementById('mainBtn');
    const loading = document.getElementById('mainLoading');
    const error = document.getElementById('mainError');
    const resultArea = document.getElementById('resultArea');
    error.style.display = 'none';
    loading.style.display = 'block';
    btn.disabled = true;
    resultArea.style.display = 'none';
    if (!M || !K || !P || M<=0 || K<=0 || P<=0) {
        error.textContent = '请输入有效的M、K、P';
        error.style.display = 'block';
        loading.style.display = 'none';
        btn.disabled = false;
        return;
    }
    try {
        // 获取预警探测能力标准值
        const N1_val = document.getElementById('inputN1').value || 0;
        const N2_val = document.getElementById('inputN2').value || 0;
        const N3_val = document.getElementById('inputN3').value || 0;
        const N4_val = document.getElementById('inputN4').value || 0;
        const N5_val = document.getElementById('inputN5').value || 0;
        
        // 获取地图数据
        const mapRes = await fetch('/map_data', {
            method: 'POST',
            headers: {'Content-Type':'application/json'},
            body: JSON.stringify({
                M: parseInt(M), 
                K: parseInt(K), 
                P: parseInt(P),
                N1_val: parseInt(N1_val),
                N2_val: parseInt(N2_val),
                N3_val: parseInt(N3_val),
                N4_val: parseInt(N4_val),
                N5_val: parseInt(N5_val)
            })
        });
        const mapData = await mapRes.json();
        if (!mapRes.ok) throw new Error(mapData.error||'地图生成失败');
        
        // 直接打印 team_result 内容，以便调试
        console.log("原始 team_result 数据:", mapData.team_result);
        console.log("teams 数据:", mapData.team_result.teams);
        console.log("capacity_needed 数据:", mapData.team_result.capacity_needed);
        console.log("total_teams 数据:", mapData.team_result.total_teams);
        
        // 直接从mapData中提取维修小队统计数据
        const team_result = mapData.team_result;
        
        // 显示维修小队结果
        showTeamSummary(team_result);
        resultArea.style.display = 'block';
        
        // 绘制地图
        drawMainMap(mapData);
        
        // 显示无人装备详细信息
        showStationDetail(mapData.equipments);
        
        // 显示威胁点详细信息
        showThreatDetail(mapData.threats);
        
        // 显示集中营详细信息
        showCampDetail(mapData.camps);
        
        // 显示集中营分配明细
        showCampAssignDetail(mapData.camp_assignments);
        
        // 显示路径与时间成本
        showPathDetail(mapData.paths, mapData.total_time);
        // 显示能力动态变化折线图
        showNAbilityChart(mapData.n_ability_timeline, mapData.n_ability_benchmarks);
        
        // 显示小队维修过程详情
        showTeamProcessDetail(mapData.team_processes);
        
        loading.style.display = 'none';
        btn.disabled = false;
    } catch(e) {
        error.textContent = e.message||'网络错误或服务异常';
        error.style.display = 'block';
        loading.style.display = 'none';
        btn.disabled = false;
    }
}

// 显示维修小队结果
function showTeamSummary(data) {
    const teamSummary = document.getElementById('mainTeamSummary');
    if (!teamSummary) return;
    
    console.log("维修小队数据:", data); // 调试日志
    
    let html = '';
    const equipmentTypes = ['Z1', 'Z2', 'Z3', 'Z4', 'Z5'];
    const teamTypes = ['B1', 'B2', 'B3', 'B4', 'B5'];
    
    // 确保 teams 对象存在，如果不存在则尝试从其他属性获取
    let teams = {};
    let teams_by_b = {};
    let capacity_needed = {};
    let total_teams = 0;
    
    // 处理不同的数据结构
    if (data.teams_by_b) {
        // 优先使用 teams_by_b 数据，这是为 B1-B5 准备的
        teams_by_b = data.teams_by_b;
        teams = data.teams || {};
        capacity_needed = data.capacity_needed || {};
        total_teams = data.total_teams || 0;
    } else if (data.teams) {
        // 后端返回的是 {'Z1': 0, 'Z2': 1, ...} 格式，需要转换为 {'B1': 0, 'B2': 1, ...}
        teams = data.teams;
        equipmentTypes.forEach((typ, index) => {
            const teamTyp = teamTypes[index];
            teams_by_b[teamTyp] = teams[typ] || 0;
        });
        capacity_needed = data.capacity_needed || {};
        total_teams = data.total_teams || 0;
    } else {
        // 尝试从 teams_1, teams_2, teams_3 中获取数据
        if (data.teams_1) {
            Object.assign(teams, data.teams_1);
        }
        
        if (data.teams_2) {
            Object.assign(teams, data.teams_2);
        }
        
        if (data.teams_3) {
            Object.assign(teams, data.teams_3);
        }
        
        // 转换为 B1-B5 格式
        equipmentTypes.forEach((typ, index) => {
            const teamTyp = teamTypes[index];
            teams_by_b[teamTyp] = teams[typ] || 0;
        });
        
        total_teams = data.total_teams || Object.values(teams).reduce((a, b) => a + b, 0);
        
        // 尝试获取容量需求
        if (data.stations_detail) {
            data.stations_detail.forEach(station => {
                const type = station.type;
                if (!capacity_needed[type]) capacity_needed[type] = 0;
                capacity_needed[type] += station.repair_cost || 0;
            });
        }
    }
    
    console.log("处理后的维修小队数据:", {teams, teams_by_b, capacity_needed, total_teams}); // 调试日志
    
    equipmentTypes.forEach((typ, index) => {
        const teamTyp = teamTypes[index];
        html += `
            <div class="team-card">
                <h4>${teamTyp} (for ${typ})</h4>
                <div class="team-count">${teams_by_b[teamTyp] || 0}</div>
                <p>总需求: ${capacity_needed[typ] || 0} 点</p>
            </div>
        `;
    });
    
    html += `
        <div class="team-card" style="grid-column: 1 / -1; background: linear-gradient(135deg, #ff6b6b, #ee5a24);">
            <h4>总维修小队</h4>
            <div class="team-count">${total_teams}</div>
            <p>总维修能力需求: ${Object.values(capacity_needed).reduce((a,b)=>a+b,0) || 0} 点</p>
        </div>
    `;
    
    teamSummary.innerHTML = html;
}

// 添加重新计算并生成地图的函数
function recalculateAndRedraw() {
    // 显示加载中
    const loading = document.getElementById('mainLoading');
    loading.style.display = 'block';
    
    // 获取当前的数据
    const currentData = window.lastMapData;
    if (!currentData) {
        alert('没有可用的地图数据');
        loading.style.display = 'none';
        return;
    }
    
    // 准备请求数据
    const M = currentData.equipments.length;
    const K = currentData.threats.length;
    const P = currentData.camps.length;
    
    // 获取预警探测能力标准值
    const N1_val = document.getElementById('inputN1').value || 0;
    const N2_val = document.getElementById('inputN2').value || 0;
    const N3_val = document.getElementById('inputN3').value || 0;
    const N4_val = document.getElementById('inputN4').value || 0;
    const N5_val = document.getElementById('inputN5').value || 0;
    
    // 从当前数据中提取维修站和威胁点的信息
    const equipmentPositions = currentData.equipments.map(s => ({
        id: s.id,
        type: s.type,
        damaged_components: s.damaged_components,
        state: s.state,
        abilities: s.abilities,
        repair_cost: s.repair_cost,
        x: s.x,
        y: s.y
    }));
    
    const threatPositions = currentData.threats.map(t => ({
        id: t.id,
        name: t.name || `威胁点${String(t.id).padStart(2, '0')}`,
        range: t.range,
        value: t.value || 100, // 添加默认值100
        x: t.x,
        y: t.y
    }));
    
    // 发送请求重新计算
    fetch('/map_data', {
        method: 'POST',
        headers: {'Content-Type':'application/json'},
        body: JSON.stringify({
            M: M,
            K: K,
            P: P,
            N1_val: parseInt(N1_val),
            N2_val: parseInt(N2_val),
            N3_val: parseInt(N3_val),
            N4_val: parseInt(N4_val),
            N5_val: parseInt(N5_val),
            equipmentPositions: equipmentPositions,
            threatPositions: threatPositions
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        
        // 直接打印 team_result 内容，以便调试
        console.log("重新计算 - 原始 team_result 数据:", data.team_result);
        console.log("重新计算 - teams 数据:", data.team_result.teams);
        console.log("重新计算 - teams_by_b 数据:", data.team_result.teams_by_b);
        console.log("重新计算 - capacity_needed 数据:", data.team_result.capacity_needed);
        console.log("重新计算 - total_teams 数据:", data.team_result.total_teams);
        
        // 更新地图数据
        window.lastMapData = data;
        
        // 显示维修小队结果
        showTeamSummary(data.team_result);
        
        // 绘制地图
        drawMainMap(data);
        
        // 显示无人装备详细信息
        showStationDetail(data.equipments);
        
        // 显示威胁点详细信息
        showThreatDetail(data.threats);
        
        // 显示集中营详细信息
        showCampDetail(data.camps);
        
        // 显示集中营分配明细
        showCampAssignDetail(data.camp_assignments);
        
        // 显示路径与时间成本
        showPathDetail(data.paths, data.total_time);
        // 显示能力动态变化折线图
        showNAbilityChart(data.n_ability_timeline, data.n_ability_benchmarks);
        
        // 显示小队维修过程详情
        showTeamProcessDetail(data.team_processes);
        
        // 隐藏加载中
        loading.style.display = 'none';
        
        // 显示结果区域
        document.getElementById('resultArea').style.display = 'block';
    })
    .catch(error => {
        alert('重新计算失败: ' + error.message);
        loading.style.display = 'none';
    });
}

// 新增：能力动态变化折线图渲染
function showNAbilityChart(timeline, benchmarks) {
    const area = document.getElementById('nAbilityChartArea');
    const chartDiv = document.getElementById('nAbilityChart');
    if (!area || !chartDiv) return;
    if (!timeline || !timeline.length) {
        area.style.display = 'none';
        return;
    }
    area.style.display = 'block';
    // 动态加载ECharts
    if (typeof echarts === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        script.onload = () => renderChart();
        document.body.appendChild(script);
    } else {
        renderChart();
    }
    function renderChart() {
        const myChart = echarts.init(chartDiv);
        const times = timeline.map(item => item.time);
        const n1 = timeline.map(item => item.N1);
        const n2 = timeline.map(item => item.N2);
        const n3 = timeline.map(item => item.N3);
        const n4 = timeline.map(item => item.N4);
        const n5 = timeline.map(item => item.N5);
        // 构造5条基准线
        let markLines = [];
        if (benchmarks) {
            Object.entries(benchmarks).forEach(([key, val]) => {
                if (val > 0) {  // 只显示大于0的阈值线
                    markLines.push({
                        yAxis: val,
                        name: key + '阈值',
                        lineStyle: { type: 'dashed', color: '#999' },
                        label: { formatter: key + '阈值', position: 'end' }
                    });
                }
            });
        }
        
        // 调试信息
        console.log('基准值数据:', benchmarks);
        console.log('阈值线数据:', markLines);
        const option = {
            tooltip: { trigger: 'axis' },
            legend: { data: ['N1','N2','N3','N4','N5'] },
            xAxis: { type: 'category', name: '时间(h)', data: times },
            yAxis: { type: 'value', name: '能力值' },
            series: [
                { name: 'N1', type: 'line', data: n1 },
                { name: 'N2', type: 'line', data: n2 },
                { name: 'N3', type: 'line', data: n3 },
                { name: 'N4', type: 'line', data: n4 },
                { name: 'N5', type: 'line', data: n5 }
            ],
            markLine: { data: markLines }
        };
        // markLine要加到每个series上
        option.series.forEach(s => { s.markLine = { data: markLines }; });
        myChart.setOption(option);
    }
}

// 添加按钮事件监听
window.addEventListener('DOMContentLoaded', function() {
    const addEquipmentBtn = document.getElementById('addEquipmentBtn');
    if (addEquipmentBtn) {
        addEquipmentBtn.addEventListener('click', function() {
            showAddEquipmentDialog();
        });
    }
    const addThreatBtn = document.getElementById('addThreatBtn');
    if (addThreatBtn) {
        addThreatBtn.addEventListener('click', function() {
            showAddThreatDialog();
        });
    }
});
    
// 弹窗添加无人装备
function showAddEquipmentDialog() {
    const html = `
        <div class="modal-mask" style="position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.2);z-index:9999;display:flex;align-items:center;justify-content:center;">
            <div class="modal-dialog" style="background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;">
                <h3>添加无人装备</h3>
                <div style="margin-bottom:10px;">
                    类型: <select id="addEqType">
                        ${(window.EQUIPMENT_TYPES||['Z1','Z2','Z3','Z4','Z5']).map(t=>`<option value='${t}'>${t}</option>`).join('')}
                    </select>
                </div>
                <div style="margin-bottom:10px;">坐标X: <input id="addEqX" type="number" step="0.01"></div>
                <div style="margin-bottom:10px;">坐标Y: <input id="addEqY" type="number" step="0.01"></div>
                <div style="margin-bottom:10px;">损坏部件(逗号分隔s1-s5): <input id="addEqDamaged" type="text" placeholder="如s1,s3"></div>
                <div style="text-align:right;">
                    <button id="addEqCancel">取消</button>
                    <button id="addEqConfirm">确认</button>
                </div>
            </div>
        </div>
    `;
    const mask = document.createElement('div');
    mask.innerHTML = html;
    document.body.appendChild(mask);
    mask.querySelector('#addEqCancel').onclick = () => document.body.removeChild(mask);
    mask.querySelector('#addEqConfirm').onclick = () => {
        const type = mask.querySelector('#addEqType').value;
        const x = parseFloat(mask.querySelector('#addEqX').value);
        const y = parseFloat(mask.querySelector('#addEqY').value);
        const damaged = mask.querySelector('#addEqDamaged').value.split(',').map(s=>s.trim()).filter(Boolean);
        if(isNaN(x)||isNaN(y)||!type){alert('请填写完整');return;}
        // 生成新id
        const newId = (window.lastMapData.equipments.reduce((max,e)=>Math.max(max,e.id),0)||0)+1;
        // 计算状态
        let state = '完好';
        if(damaged.length===1) state='轻微损伤';
        else if(damaged.length===2) state='中度损伤';
        else if(damaged.length>=3) state='严重损伤';
        // 能力（简单置零，刷新时会重算）
        let abilities = {N1:0,N2:0,N3:0,N4:0,N5:0};
        // 添加到数据
        window.lastMapData.equipments.push({id:newId,type,damaged_components:damaged,state,abilities,x,y,repair_cost:damaged.length*2});
        window.showStationDetail(window.lastMapData.equipments);
        drawMainMap(window.lastMapData);
        document.body.removeChild(mask);
    };
}
// 弹窗添加威胁点
function showAddThreatDialog() {
    const html = `
        <div class="modal-mask" style="position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.2);z-index:9999;display:flex;align-items:center;justify-content:center;">
            <div class="modal-dialog" style="background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;">
                <h3>添加威胁点</h3>
                <div style="margin-bottom:10px;">坐标X: <input id="addThX" type="number" step="0.01"></div>
                <div style="margin-bottom:10px;">坐标Y: <input id="addThY" type="number" step="0.01"></div>
                <div style="margin-bottom:10px;">威胁范围(km): <input id="addThRange" type="number" step="0.01" value="1.0"></div>
                <div style="margin-bottom:10px;">威胁值: <input id="addThValue" type="number" value="100"></div>
                <div style="text-align:right;">
                    <button id="addThCancel">取消</button>
                    <button id="addThConfirm">确认</button>
                </div>
            </div>
        </div>
    `;
    const mask = document.createElement('div');
    mask.innerHTML = html;
    document.body.appendChild(mask);
    mask.querySelector('#addThCancel').onclick = () => document.body.removeChild(mask);
    mask.querySelector('#addThConfirm').onclick = () => {
        const x = parseFloat(mask.querySelector('#addThX').value);
        const y = parseFloat(mask.querySelector('#addThY').value);
        const range = parseFloat(mask.querySelector('#addThRange').value);
        const value = parseFloat(mask.querySelector('#addThValue').value);
        if(isNaN(x)||isNaN(y)||isNaN(range)||isNaN(value)){alert('请填写完整');return;}
        // 生成新id
        const newId = (window.lastMapData.threats.reduce((max,e)=>Math.max(max,e.id),0)||0)+1;
        // 添加到数据
        window.lastMapData.threats.push({id:newId,name:`威胁点${String(newId).padStart(2,'0')}`,range,value,x,y});
        window.showThreatDetail(window.lastMapData.threats);
        drawMainMap(window.lastMapData);
        document.body.removeChild(mask);
    };
} 