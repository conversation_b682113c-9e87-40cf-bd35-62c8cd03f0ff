// 显示无人装备详细信息
window.showStationDetail = function(equipments) {
    const area = document.getElementById('stationDetailArea');
    const table = document.getElementById('stationDetailTable');
    if (table) table.innerHTML = '';
    equipments.forEach((s, i) => {
        let statusColor = window.STATE_COLOR_MAP && window.STATE_COLOR_MAP[s.state] ? window.STATE_COLOR_MAP[s.state] : '#ccc';
        if (table) table.innerHTML += `<tr data-id="${s.id}">
            <td>${s.id}</td>
            <td>${s.type}</td>
            <td>${s.damaged_components.join(', ')}</td>
            <td><span class='station-status-dot' style='background:${statusColor}'></span>${s.state}</td>
            <td>${Object.entries(s.abilities).map(([k,v]) => `${k}:${v.toFixed(2)}`).join(', ')}</td>
            <td>(${s.x.toFixed(2)}, ${s.y.toFixed(2)})</td>
            <td>
                <button class="edit-btn" onclick="editEquipment(${s.id})">编辑</button>
                <button class="delete-btn" onclick="deleteEquipment(${s.id})">删除</button>
            </td>
        </tr>`;
    });
    if (area) area.style.display = 'block';
    // 切换按钮事件
    const toggleBtn = document.getElementById('stationInfoToggle');
    const detailContent = document.getElementById('stationDetailContent');
    if (toggleBtn) toggleBtn.textContent = '展开详情';
    if (detailContent) detailContent.style.display = 'none';
    if (toggleBtn) toggleBtn.onclick = function() {
        if (detailContent && detailContent.style.display === 'none') {
            detailContent.style.display = 'block';
            toggleBtn.textContent = '收起详情';
        } else {
            detailContent.style.display = 'none';
            toggleBtn.textContent = '展开详情';
        }
    };
}

// 编辑无人装备
window.editEquipment = function(id) {
    const row = document.querySelector(`#stationDetailTable tr[data-id="${id}"]`);
    if (!row) return;
    const cells = row.cells;
    // 类型下拉
    const currentType = cells[1].textContent.trim();
    let typeOptions = '';
    (window.EQUIPMENT_TYPES||['Z1','Z2','Z3','Z4','Z5']).forEach(t => {
        typeOptions += `<option value="${t}"${t===currentType?' selected':''}>${t}</option>`;
    });
    // 坐标
    const coords = cells[5].textContent.replace(/[()]/g, '').split(',').map(c => parseFloat(c.trim()));
    const originalType = cells[1].innerHTML;
    const originalContent = cells[5].innerHTML;
    // 类型编辑
    cells[1].innerHTML = `<select id="edit-station-type-${id}">${typeOptions}</select>`;
    // 坐标编辑
    cells[5].innerHTML = `
        <input type="number" class="coordinate-input" value="${coords[0]}" step="0.01" min="-25" max="25" id="edit-station-x-${id}">
        <input type="number" class="coordinate-input" value="${coords[1]}" step="0.01" min="0" max="32" id="edit-station-y-${id}">
        <button class="save-btn" onclick="saveEquipmentEdit(${id}, '${originalType}')">保存</button>
        <button class="cancel-btn" onclick="cancelEquipmentEdit(${id}, '${originalContent}', '${originalType}')">取消</button>
    `;
    cells[6].innerHTML = `<span>编辑中...</span>`;
}

// 保存无人装备编辑
window.saveEquipmentEdit = function(id, originalType) {
    const xInput = document.getElementById(`edit-station-x-${id}`);
    const yInput = document.getElementById(`edit-station-y-${id}`);
    const typeInput = document.getElementById(`edit-station-type-${id}`);
    if (!xInput || !yInput || !typeInput) return;
    const newX = parseFloat(xInput.value);
    const newY = parseFloat(yInput.value);
    const newType = typeInput.value;
    // 验证输入
    if (isNaN(newX) || isNaN(newY)) {
        alert('请输入有效的坐标值');
        return;
    }
    // 验证坐标是否在梯形区域内
    const top = 12.0;
    const bottom = 50.0;
    const height = 32.0;
    const width = top + (bottom - top) * (newY / height);
    if (newY < 0 || newY > height || Math.abs(newX) > width/2) {
        alert('坐标超出地图范围');
        return;
    }
    // 更新数据
    const equipment = window.lastMapData.equipments.find(s => s.id === id);
    if (equipment) {
        equipment.x = newX;
        equipment.y = newY;
        equipment.type = newType;
        // 刷新表格和地图
        window.showStationDetail(window.lastMapData.equipments);
        drawMainMap(window.lastMapData);
    }
}

// 取消无人装备编辑
window.cancelEquipmentEdit = function(id, originalContent, originalType) {
    const row = document.querySelector(`#stationDetailTable tr[data-id="${id}"]`);
    if (!row) return;
    row.cells[1].innerHTML = originalType;
    row.cells[5].innerHTML = originalContent;
    row.cells[6].innerHTML = `
        <button class="edit-btn" onclick="editEquipment(${id})">编辑</button>
        <button class="delete-btn" onclick="deleteEquipment(${id})">删除</button>
    `;
}

// 删除维修站
function deleteEquipment(id) {
    if (!confirm(`确定要删除维修站 #${id} 吗？`)) return;
    
    // 从数据中删除
    const equipmentIndex = window.lastMapData.equipments.findIndex(s => s.id === id);
    if (equipmentIndex !== -1) {
        window.lastMapData.equipments.splice(equipmentIndex, 1);
        
        // 从表格中删除
        const row = document.querySelector(`#stationDetailTable tr[data-id="${id}"]`);
        if (row) row.remove();
        
        // 重绘地图
        drawMainMap(window.lastMapData);
    }
} 