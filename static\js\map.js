// 地图缩放与拖动参数
let mapScale = 1.0;
let mapOffset = {x:0, y:0};
let isDragging = false;
let dragStart = {x:0, y:0};
let lastOffset = {x:0, y:0};

// 地图绘制（带缩放拖动）
(function(){
    const canvas = document.getElementById('mainMapCanvas');
    canvas.addEventListener('mousedown', function(e){
        isDragging = true;
        dragStart = {x: e.offsetX, y: e.offsetY};
        lastOffset = {...mapOffset};
        canvas.style.cursor = 'grabbing';
    });
    canvas.addEventListener('mousemove', function(e){
        if(isDragging) {
            mapOffset.x = lastOffset.x + (e.offsetX - dragStart.x);
            mapOffset.y = lastOffset.y + (e.offsetY - dragStart.y);
            if(window.lastMapData) drawMainMap(window.lastMapData);
        }
    });
    canvas.addEventListener('mouseup', function(){
        isDragging = false;
        canvas.style.cursor = 'grab';
    });
    canvas.addEventListener('mouseleave', function(){
        isDragging = false;
        canvas.style.cursor = 'grab';
    });
    canvas.addEventListener('wheel', function(e){
        e.preventDefault();
        const scale = e.deltaY < 0 ? 1.1 : 0.9;
        mapScale *= scale;
        mapScale = Math.max(0.3, Math.min(3, mapScale));
        if(window.lastMapData) drawMainMap(window.lastMapData);
    });
})();

// 保存上次地图数据，便于缩放拖动重绘
function drawMainMap(data) {
    window.lastMapData = data;
    const canvas = document.getElementById('mainMapCanvas');
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0,0,canvas.width,canvas.height);
    const {top, bottom, height} = data.trapezoid;
    const margin = 40;
    const scaleY = (canvas.height-2*margin)/height * mapScale;
    const scaleX = (canvas.width-2*margin)/bottom * mapScale;
    function mapToCanvas(x, y) {
        const cy = margin + y*scaleY + mapOffset.y;
        const cx = canvas.width/2 + x*scaleX + mapOffset.x;
        return [cx, cy];
    }
    // 画坐标轴
    ctx.save();
    ctx.strokeStyle = '#bbb';
    ctx.lineWidth = 1;
    // y轴
    ctx.beginPath();
    ctx.moveTo(canvas.width/2+mapOffset.x, margin);
    ctx.lineTo(canvas.width/2+mapOffset.x, canvas.height-margin);
    ctx.stroke();
    // x轴
    ctx.beginPath();
    ctx.moveTo(margin, canvas.height-margin+mapOffset.y);
    ctx.lineTo(canvas.width-margin, canvas.height-margin+mapOffset.y);
    ctx.stroke();
    // x轴刻度
    ctx.font = '12px Arial';
    ctx.fillStyle = '#888';
    for(let x=-bottom/2;x<=bottom/2;x+=10){
        let [cx,cy]=mapToCanvas(x,0);
        ctx.beginPath();ctx.moveTo(cx,canvas.height-margin-5);ctx.lineTo(cx,canvas.height-margin+5);ctx.stroke();
        ctx.fillText(x.toFixed(0),cx-8,canvas.height-margin+20);
    }
    // y轴刻度
    for(let y=0;y<=height;y+=8){
        let [cx,cy]=mapToCanvas(0,y);
        ctx.beginPath();ctx.moveTo(canvas.width/2-5+mapOffset.x,cy);ctx.lineTo(canvas.width/2+5+mapOffset.x,cy);ctx.stroke();
        ctx.fillText(y.toFixed(0),canvas.width/2+10+mapOffset.x,cy+4);
    }
    ctx.restore();
    // 画梯形边界
    ctx.save();
    ctx.strokeStyle = '#888';
    ctx.lineWidth = 2;
    ctx.beginPath();
    let [x1,y1] = mapToCanvas(-top/2,0);
    let [x2,y2] = mapToCanvas(top/2,0);
    let [x3,y3] = mapToCanvas(bottom/2,height);
    let [x4,y4] = mapToCanvas(-bottom/2,height);
    ctx.moveTo(x1,y1);
    ctx.lineTo(x2,y2);
    ctx.lineTo(x3,y3);
    ctx.lineTo(x4,y4);
    ctx.closePath();
    ctx.stroke();
    ctx.restore();
    
    // 画维修站（三角形）
    for(const s of data.equipments) {
        const [cx,cy] = mapToCanvas(s.x, s.y);
        ctx.save();
        // 状态颜色
        let fillColor = window.STATE_COLOR_MAP && window.STATE_COLOR_MAP[s.state] ? window.STATE_COLOR_MAP[s.state] : '#ccc';
        // 类型边框
        let borderColor = window.EQUIPMENT_TYPE_COLORS && window.EQUIPMENT_TYPE_COLORS[s.type] ? window.EQUIPMENT_TYPE_COLORS[s.type] : '#888';
        
        // 检查该维修站是否需要维修且未分配
        const needsRepair = s.state !== '完好' && (s.repair_cost > 0 || s.damaged_components?.length > 0);
        const isAssigned = data.paths.some(p => p.equipment_id === s.id && p.camp_id);
        
        ctx.beginPath();
        ctx.moveTo(cx, cy-12*mapScale);
        ctx.lineTo(cx-10*mapScale, cy+8*mapScale);
        ctx.lineTo(cx+10*mapScale, cy+8*mapScale);
        ctx.closePath();
        ctx.fillStyle = fillColor;
        ctx.fill();
        ctx.lineWidth = 3;
        ctx.strokeStyle = borderColor;
        ctx.stroke();
        
        // 为需要维修但未分配的维修站添加特殊标记
        if (needsRepair && !isAssigned) {
            ctx.beginPath();
            ctx.arc(cx, cy, 16*mapScale, 0, 2*Math.PI);
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.font = `${14*mapScale}px Arial`;
            ctx.fillStyle = '#ff0000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('!', cx, cy-18*mapScale);
        }
        
        ctx.restore();
    }
    // 画威胁点
    for(const t of data.threats) {
        const [cx,cy] = mapToCanvas(t.x, t.y);
        ctx.save();
        
        // 绘制威胁渐变区域
        const gradient = ctx.createRadialGradient(cx, cy, 0, cx, cy, t.range*scaleX);
        gradient.addColorStop(0, 'rgba(255,0,0,0.7)');  // 中心处较深的红色
        gradient.addColorStop(0.3, 'rgba(255,0,0,0.5)'); // 30%处的中等红色
        gradient.addColorStop(0.7, 'rgba(255,0,0,0.2)'); // 70%处的浅红色
        gradient.addColorStop(1, 'rgba(255,0,0,0)');    // 边缘完全透明
        
        ctx.beginPath();
        ctx.arc(cx, cy, t.range*scaleX, 0, 2*Math.PI);
        ctx.fillStyle = gradient;
        ctx.fill();
        
        // 绘制威胁点本身
        ctx.fillStyle = '#ff0000';
        ctx.beginPath();
        ctx.arc(cx, cy, 8*mapScale, 0, 2*Math.PI);
        ctx.fill();
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 绘制威胁点范围指示线（半透明环）
        ctx.beginPath();
        ctx.arc(cx, cy, t.range*scaleX, 0, 2*Math.PI);
        ctx.strokeStyle = 'rgba(255,0,0,0.3)';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        ctx.restore();
    }
    // 画集中营（星型）
    for(const c of data.camps) {
        const [cx,cy] = mapToCanvas(c.x, c.y);
        ctx.save();
        ctx.font = `${20*mapScale}px Arial`;
        ctx.fillStyle = '#fbc02d';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('★', cx, cy);
        ctx.restore();
    }
    // 画路径
    ctx.save();
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#2196f3';
    
    // 获取所有集中营和装备的坐标
    const campPositions = {};
    for (const c of data.camps || []) {
        campPositions[c.id] = {x: c.x, y: c.y};
    }
    
    // 遍历路径，只绘制集中营到装备的连接线
    for (const p of data.paths || []) {
        if (p.camp_id && typeof p.camp_x === 'number' && typeof p.camp_y === 'number' && 
            typeof p.equipment_x === 'number' && typeof p.equipment_y === 'number') {
            
            // 确认起点是集中营坐标
            const campInfo = campPositions[p.camp_id];
            if (!campInfo) continue;
            
            // 使用集中营的实际坐标作为起点
            const [x1, y1] = mapToCanvas(campInfo.x, campInfo.y);
            const [x2, y2] = mapToCanvas(p.equipment_x, p.equipment_y);
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }
    ctx.restore();
}

// 重新计算威胁热力图
function recalculateThreatHeatmap() {
    // 为了性能考虑，我们不再使用grid数组，因为我们现在直接使用径向渐变绘制威胁区域
    // 清除旧的grid数据
    window.lastMapData.grid = [];
} 