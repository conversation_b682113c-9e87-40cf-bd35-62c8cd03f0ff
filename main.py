from flask import Flask, render_template, request, jsonify
import random
import math
from config import EQUIPMENT_TYPES, STATE_TEXT, CONTRIBUTION_TABLE, ABILITIES, COMPONENT_REPAIR_COST, TEAM_CAPACITY, TEAM_SPEED, TRAPEZOID, COMPONENT_REPAIR_TIME
from models.utils import trapezoid_random_point
from models.station import generate_equipments, calculate_repair_teams, place_equipments_on_map
from models.threat import generate_threats, calculate_threat_heatmap
from models.camp import generate_camps, calculate_camp_paths
from models.optimizer import optimal_camp_assignment

app = Flask(__name__)
# 初始化全局优先级列表配置
app.config['CURRENT_PRIORITY_LIST'] = ['N1', 'N2', 'N3', 'N4', 'N5']

@app.route('/')
def index():
    from config import TEAM_TYPES, TEAM_TYPE_NAMES, STATE_COLOR_MAP, EQUIPMENT_TYPE_COLORS, EQUIPMENT_TYPES
    return render_template('index.html', TEAM_TYPES=TEAM_TYPES, TEAM_TYPE_NAMES=TEAM_TYPE_NAMES, STATE_COLOR_MAP=STATE_COLOR_MAP, EQUIPMENT_TYPE_COLORS=EQUIPMENT_TYPE_COLORS, EQUIPMENT_TYPES=EQUIPMENT_TYPES)

@app.route('/calculate', methods=['POST'])
def calculate():
    try:
        req_json = request.get_json(force=True) or {}
        M = req_json.get('M')
        if M is None or not isinstance(M, int) or M <= 0:
            return jsonify({'error': '维修站数量必须为正整数'}), 400
        
        # 使用与map_data相同的随机种子，确保生成相同的维修站
        random.seed(M)
        
        # 生成维修站
        equipments = generate_equipments(M)
        
        # 计算维修小队
        result = calculate_repair_teams(equipments, [])  # empty priority, perhaps calculate full repair
        # But to fit, perhaps add default priority ['N1','N2','N3','N4','N5']
        # station_details to equipment_details with new fields

        # But since demand has priorities in input for execution, perhaps remove /calculate or make it basic.

        # For /map_data
        # Add to req_json: N1_val = req_json.get('N1_val', 0), etc.
        # ability_vals = {'N1': N1_val, 'N2': N2_val, 'N3': N3_val, 'N4': N4_val, 'N5': N5_val}
        # priority_list = sorted(ABILITIES, key=lambda ab: ability_vals[ab], reverse=True)
        # After generating equipments (use generate_equipments and place if not custom)
        # For custom, add logic to calculate damaged, abilities if not present.
        # team_result = calculate_repair_teams(equipments, priority_list)
        # Update print and jsonify to 'equipments': equipments, ...
        # In unassigned, use equipment_id, type, state
        
        # 准备详细数据用于显示
        equipment_details = []
        for i, equipment in enumerate(equipments):
            equipment_details.append({
                'id': i + 1,
                'type': equipment['type'],
                'damage': equipment['damage_text'],
                'cost': equipment['cost']
            })
        
        result['equipment_details'] = equipment_details
        
        # 重置随机种子，避免影响其他接口
        random.seed()
        
        return jsonify(result)
    
    except ValueError:
        return jsonify({'error': '请输入有效的数字'}), 400
    except Exception as e:
        return jsonify({'error': f'计算错误: {str(e)}'}), 500

@app.route('/map_data', methods=['POST'])
def map_data():
    try:
        req_json = request.get_json(force=True) or {}
        M = req_json.get('M')
        K = req_json.get('K')
        P = req_json.get('P', 5)
        # 获取自定义位置
        custom_stations = req_json.get('stationPositions', [])
        custom_threats = req_json.get('threatPositions', [])
        
        if M is None or not isinstance(M, int) or M <= 0:
            return jsonify({'error': '维修站数量(M)必须为正整数'}), 400
        if K is None or not isinstance(K, int) or K <= 0:
            return jsonify({'error': '威胁点数量(K)必须为正整数'}), 400
        if P is None or not isinstance(P, int) or P <= 0:
            return jsonify({'error': '集中营数量(P)必须为正整数'}), 400
        
        # 使用与calculate相同的随机种子，确保生成相同的维修站
        random.seed(M)
        
        # 维修站处理
        equipment_positions = req_json.get('equipmentPositions')
        if equipment_positions:
            # 补全所有字段
            for e in equipment_positions:
                if 'damaged_components' not in e:
                    e['damaged_components'] = []
                if 'state' not in e:
                    n = len(e['damaged_components'])
                    if n == 0: e['state'] = '完好'
                    elif n == 1: e['state'] = '轻微损伤'
                    elif n == 2: e['state'] = '中度损伤'
                    else: e['state'] = '严重损伤'
                if 'abilities' not in e:
                    e['abilities'] = {'N1':0,'N2':0,'N3':0,'N4':0,'N5':0}
                if 'repair_cost' not in e:
                    e['repair_cost'] = len(e['damaged_components'])*2
                if 'damage_text' not in e:
                    e['damage_text'] = e['state']
            equipments = equipment_positions
            equipment_set = {(round(e['x'], 4), round(e['y'], 4)) for e in equipments}
        elif custom_stations:
            # 如果有自定义维修站位置，直接使用
            equipments = custom_stations
            # 确保每个站点都有必要的属性
            for e in equipments:
                if 'cost' not in e and 'damage_level' in e:
                    e['cost'] = e['damage_level']
                if 'damage_text' not in e and 'damage_level' in e:
                    e['damage_text'] = STATE_TEXT.get(e['damage_level'], '未知')
                if 'damaged_components' not in e:
                    pass # Placeholder for actual computation if needed
            equipment_set = {(round(e['x'], 4), round(e['y'], 4)) for e in equipments}
        else:
            # 否则生成并放置维修站
            initial_equipments = generate_equipments(M)
            equipments, equipment_set = place_equipments_on_map(initial_equipments, trapezoid_random_point)
        
        # 确保所有装备都包含damage_text字段
        for e in equipments:
            if 'damage_text' not in e:
                e['damage_text'] = e.get('state', '')

        # 威胁点处理
        if custom_threats:
            # 如果有自定义威胁点位置，直接使用
            threats = custom_threats
            # 确保每个威胁点都有必要的属性
            for t in threats:
                if 'value' not in t:
                    t['value'] = 100
                if 'name' not in t:
                    t['name'] = f'威胁点{str(t["id"]).zfill(2)}'
            threat_set = {(round(t['x'], 4), round(t['y'], 4)) for t in threats}
        else:
            # 否则生成威胁点
            threats, threat_set = generate_threats(K, equipment_set, trapezoid_random_point)
        
        # 计算全局维修小队数量
        # Add to req_json: N1_val = req_json.get('N1_val', 0), etc.
        N1_val = req_json.get('N1_val', 0)
        N2_val = req_json.get('N2_val', 0)
        N3_val = req_json.get('N3_val', 0)
        N4_val = req_json.get('N4_val', 0)
        N5_val = req_json.get('N5_val', 0)
        ability_vals = {'N1': N1_val, 'N2': N2_val, 'N3': N3_val, 'N4': N4_val, 'N5': N5_val}
        priority_list = sorted(ability_vals.keys(), key=lambda ab: ability_vals[ab], reverse=True)
        
        # 保存当前优先级列表到应用配置
        app.config['CURRENT_PRIORITY_LIST'] = priority_list

        team_result = calculate_repair_teams(equipments, priority_list)
        
        # 使用优化算法选择集中营位置并分配小队
        print(f"开始优化集中营位置，共有维修站：{len(equipments)}个，威胁点：{len(threats)}个，集中营数量：{P}个")
        camps, camp_assignments, all_paths, total_time, n_ability_timeline, n_ability_benchmarks, team_processes = optimal_camp_assignment(equipments, threats, P, TRAPEZOID, team_result, priority_list, COMPONENT_REPAIR_TIME)
        print(f"优化后的集中营位置：")
        for camp in camps:
            print(f"  集中营{camp['id']}：({camp['x']:.2f}, {camp['y']:.2f})")
        
        # 计算威胁分布热力图
        grid = calculate_threat_heatmap(threats, TRAPEZOID)
        
        # 计算每个维修站的路径和时间成本
        # paths, total_time, _ = calculate_camp_paths(camps, equipments, threats, TEAM_SPEED)
        
        # 生成N1-N5随时间变化的折线图数据
        # n_ability_timeline, n_ability_benchmarks = simulate_repair_timeline(equipments, priority_list, paths, component_repair_time=COMPONENT_REPAIR_TIME)
        
        # 添加未分配的维修站到路径列表中
        unassigned_equipments = []
        for e in equipments:
            # 检查该维修站是否已在all_paths中
            if not any(p.get('equipment_id') == e['id'] for p in all_paths):
                # 确保damage_text字段
                damage_text = e.get('damage_text', e.get('state', ''))
                unassigned_equipments.append({
                    'equipment_id': e['id'],
                    'equipment_type': e['type'],
                    'equipment_state': damage_text,
                    'camp_id': None,
                    'camp_name': None,
                    'equipment_x': e['x'],
                    'equipment_y': e['y'],
                    'distance': 0,
                    'time': 0
                })
        
        # 合并未分配的维修站到路径列表
        all_paths.extend(unassigned_equipments)
        
        # 打印 team_result 内容，以便调试
        print("DEBUG - team_result 内容:")
        print(f"  teams: {team_result.get('teams', {})}")
        print(f"  capacity_needed: {team_result.get('capacity_needed', {})}")
        print(f"  total_teams: {team_result.get('total_teams', 0)}")
        
        return jsonify({
            'equipments': equipments,
            'threats': threats,
            'camps': camps,
            'camp_assignments': camp_assignments,
            'paths': all_paths,
            'total_time': round(total_time, 1),
            'grid': grid,
            'trapezoid': TRAPEZOID,
            'team_result': team_result,
            'n_ability_timeline': n_ability_timeline,
            'n_ability_benchmarks': n_ability_benchmarks,
            'team_processes': team_processes
        })
    except Exception as e:
        return jsonify({'error': f'计算错误: {str(e)}'}), 500

if __name__ == '__main__':
    import sys
    port = 5050  # 默认端口
    # 支持命令行参数 --port
    for idx, arg in enumerate(sys.argv):
        if arg == '--port' and idx + 1 < len(sys.argv):
            port = int(sys.argv[idx + 1])
    app.run(debug=True, host='0.0.0.0', port=port) 