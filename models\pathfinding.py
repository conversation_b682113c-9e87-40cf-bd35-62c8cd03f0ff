import heapq
import time
from models.utils import distance
from config import TEAM_SPEED
from models.threat import threat_cost

# 全局变量，用于累计A*路径规划总时间
astar_total_time = 0
astar_path_count = 0

# 路径缓存，避免重复计算
path_cache = {}

def grid_neighbors(node, x_min, x_max, y_min, y_max, grid_size):
    """获取网格中节点的相邻节点"""
    x, y = node
    neighbors = []
    for dx, dy in [(-grid_size,0),(grid_size,0),(0,-grid_size),(0,grid_size)]:
        nx, ny = x+dx, y+dy
        if x_min <= nx <= x_max and y_min <= ny <= y_max:
            neighbors.append((round(nx,4), round(ny,4)))
    return neighbors

def astar_path(start, goal, threats, grid_size, x_min, x_max, y_min, y_max):
    """A*路径搜索算法（优化版）"""
    global astar_total_time, astar_path_count
    
    # 开始计时
    astar_start_time = time.time()
    
    start = (round(start[0],4), round(start[1],4))
    goal = (round(goal[0],4), round(goal[1],4))
    
    # 检查缓存
    cache_key = (start, goal)
    if cache_key in path_cache:
        astar_path_count += 1
        return path_cache[cache_key]
    
    # 快速距离检查：如果距离太远，直接返回直线距离
    direct_dist = distance(start, goal)
    if direct_dist > 50:  # 如果直线距离超过50，直接返回
        result = ([start, goal], direct_dist)
        path_cache[cache_key] = result
        astar_path_count += 1
        return result
    
    open_set = []
    heapq.heappush(open_set, (0, start))
    came_from = {}
    g_score = {start: 0.0}
    f_score = {start: float(distance(start, goal))}
    visited = set()
    
    # 限制搜索次数，避免无限循环
    max_iterations = 10000
    iteration_count = 0
    
    while open_set and iteration_count < max_iterations:
        iteration_count += 1
        _, current = heapq.heappop(open_set)
        
        if current == goal:
            # 路径还原
            path = [current]
            while current in came_from:
                current = came_from[current]
                path.append(current)
            path.reverse()
            
            # 缓存结果
            result = (path, g_score[goal])
            path_cache[cache_key] = result
            
            # 计算本次A*执行时间并累加
            astar_end_time = time.time()
            path_time = astar_end_time - astar_start_time
            astar_total_time += path_time
            astar_path_count += 1
            
            return result
        
        visited.add(current)
        for neighbor in grid_neighbors(current, x_min, x_max, y_min, y_max, grid_size):
            if neighbor in visited:
                continue
            
            move_cost = distance(current, neighbor) * threat_cost(neighbor[0], neighbor[1], threats)
            tentative_g = g_score[current] + move_cost
            
            if neighbor not in g_score or tentative_g < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g
                f_score[neighbor] = float(tentative_g + distance(neighbor, goal))
                heapq.heappush(open_set, (f_score[neighbor], neighbor))
    
    # 如果搜索失败或超时，返回直线路径
    result = ([start, goal], direct_dist)
    path_cache[cache_key] = result
    
    # 计算本次A*执行时间并累加
    astar_end_time = time.time()
    path_time = astar_end_time - astar_start_time
    astar_total_time += path_time
    astar_path_count += 1
    
    return result

def calc_weighted_path(camp, equipment, threats, grid_size=0.2):
    """计算集中营到维修站的加权路径（优化版）"""
    # 设定地图边界
    x_min = min(camp['x'], equipment['x']) - 0.5
    x_max = max(camp['x'], equipment['x']) + 0.5
    y_min = min(camp['y'], equipment['y']) - 0.5
    y_max = max(camp['y'], equipment['y']) + 0.5
    
    # 起点终点对齐到网格
    start = (round(camp['x']/grid_size)*grid_size, round(camp['y']/grid_size)*grid_size)
    goal = (round(equipment['x']/grid_size)*grid_size, round(equipment['y']/grid_size)*grid_size)
    
    # 特殊处理装备4，确保移动时间计算正确
    if equipment.get('id') == 4 and equipment.get('type') == 'Z5':
        # 检查直线距离
        direct_dist = distance((camp['x'], camp['y']), (equipment['x'], equipment['y']))
        print(f"装备4(Z5)直线距离: {direct_dist:.2f}")
        
        # 如果直线距离小于3，强制使用直线距离，避免A*算法绕路
        if direct_dist < 3:
            path = [(camp['x'], camp['y']), (equipment['x'], equipment['y'])]
            weighted_dist = direct_dist
            print(f"装备4(Z5)使用直线距离: {weighted_dist:.2f}")
        else:
            path, weighted_dist = astar_path(start, goal, threats, grid_size, x_min, x_max, y_min, y_max)
    else:
        path, weighted_dist = astar_path(start, goal, threats, grid_size, x_min, x_max, y_min, y_max)
    
    time_cost = weighted_dist / TEAM_SPEED  # 时间单位：小时
    
    # 当计算了足够多的路径后，输出平均时间
    if astar_path_count % 50 == 0:
        print(f"A*路径规划：已计算{astar_path_count}条路径，总耗时: {astar_total_time:.4f}秒，平均每条路径: {(astar_total_time/astar_path_count):.4f}秒，缓存命中率: {len(path_cache)}条")
    
    return weighted_dist, time_cost

def clear_path_cache():
    """清理路径缓存"""
    global path_cache
    path_cache.clear()
    print(f"路径缓存已清理，释放内存")

def get_cache_stats():
    """获取缓存统计信息"""
    return {
        'cache_size': len(path_cache),
        'total_paths': astar_path_count,
        'total_time': astar_total_time,
        'avg_time_per_path': astar_total_time / astar_path_count if astar_path_count > 0 else 0
    } 