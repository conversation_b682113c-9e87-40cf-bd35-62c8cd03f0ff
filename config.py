# 常量配置
TEAM_SPEED = 50  # 小队移动速度，单位km/h
# THREAT_RANGES = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]  # 威胁点辐射半径，单位km
THREAT_RANGE_MIN = 0.5  # 威胁点最小辐射半径，单位km
THREAT_RANGE_MAX = 2.0  # 威胁点最大辐射半径，单位km
THREAT_RANGE_STEP = 0.1  # 威胁点辐射半径精度，单位km
TEAM_CAPACITY = 8  # 每个维修小队维修能力

# 能力阈值控制比例，默认0.8表示修复到完好状态的80%即可，设为1.0表示所有装备都需要修复
ABILITY_THRESHOLD_RATIO = 0.8

# New constants for unmanned equipment
EQUIPMENT_TYPES = ['Z1', 'Z2', 'Z3', 'Z4', 'Z5']
TEAM_TYPES = ['B1', 'B2', 'B3', 'B4', 'B5']

COMPONENT_NAMES = {
    's1': '机体',
    's2': '控制',
    's3': '通信',
    's4': '动力',
    's5': '载荷'
}

STATE_TEXT = {
    0: '完好',
    1: '轻微损伤',
    2: '中度损伤',
    3: '严重损伤'  # for >=3 damaged components
}

ABILITY_NAMES = {
    'N1': '预警探测能力',
    'N2': '指挥控制能力',
    'N3': '打击毁伤能力',
    'N4': '通信联络能力',
    'N5': '运输投送能力'
}

# Contribution table (values converted to floats 0-1 where possible)
CONTRIBUTION_TABLE = {
    'Z1': {
        's1': {'N1': 0.25, 'N2': 0.32, 'N3': 5/70, 'N4': 0.18, 'N5': 0.65},
        's2': {'N1': 0.10, 'N2': 0.50, 'N3': 0.18, 'N4': 0.10, 'N5': 0.28},
        's3': {'N1': 0.01, 'N2': 0.45, 'N3': 0.07, 'N4': 0.50, 'N5': 0.14},
        's4': {'N1': 0.02, 'N2': 0.22, 'N3': 0.03, 'N4': 0.04, 'N5': 0.12},
        's5': {'N1': 0.03, 'N2': 0.18, 'N3': 0.02, 'N4': 0.01, 'N5': 0.07}
    },
    'Z2': {
        's1': {'N1': 0.25, 'N2': 0.15, 'N3': 5/70, 'N4': 0.25, 'N5': 0.42},
        's2': {'N1': 0.10, 'N2': 0.50, 'N3': 0.12, 'N4': 0.10, 'N5': 0.33},
        's3': {'N1': 0.01, 'N2': 0.60, 'N3': 0.09, 'N4': 0.50, 'N5': 0.22},
        's4': {'N1': 0.02, 'N2': 0.30, 'N3': 0.05, 'N4': 0.04, 'N5': 0.08},
        's5': {'N1': 0.03, 'N2': 0.25, 'N3': 0.04, 'N4': 0.01, 'N5': 0.03}
    },
    'Z3': {
        's1': {'N1': 0.25, 'N2': 0.28, 'N3': 5/70, 'N4': 0.12, 'N5': 0.78},
        's2': {'N1': 0.10, 'N2': 0.50, 'N3': 0.22, 'N4': 0.10, 'N5': 0.15},
        's3': {'N1': 0.01, 'N2': 0.38, 'N3': 0.03, 'N4': 0.50, 'N5': 0.27},
        's4': {'N1': 0.02, 'N2': 0.18, 'N3': 0.02, 'N4': 0.04, 'N5': 0.17},
        's5': {'N1': 0.03, 'N2': 0.14, 'N3': 0.01, 'N4': 0.01, 'N5': 0.09}
    },
    'Z4': {
        's1': {'N1': 0.25, 'N2': 0.40, 'N3': 5/70, 'N4': 0.08, 'N5': 0.53},
        's2': {'N1': 0.10, 'N2': 0.50, 'N3': 0.09, 'N4': 0.10, 'N5': 0.38},
        's3': {'N1': 0.01, 'N2': 0.55, 'N3': 0.12, 'N4': 0.50, 'N5': 0.05},
        's4': {'N1': 0.02, 'N2': 0.12, 'N3': 0.06, 'N4': 0.04, 'N5': 0.15},
        's5': {'N1': 0.03, 'N2': 0.28, 'N3': 0.03, 'N4': 0.01, 'N5': 0.04}
    },
    'Z5': {
        's1': {'N1': 0.25, 'N2': 0.22, 'N3': 5/70, 'N4': 0.30, 'N5': 0.88},
        's2': {'N1': 0.10, 'N2': 0.50, 'N3': 0.15, 'N4': 0.10, 'N5': 0.22},
        's3': {'N1': 0.01, 'N2': 0.65, 'N3': 0.05, 'N4': 0.50, 'N5': 0.19},
        's4': {'N1': 0.02, 'N2': 0.08, 'N3': 0.04, 'N4': 0.04, 'N5': 0.03},
        's5': {'N1': 0.03, 'N2': 0.20, 'N3': 0.02, 'N4': 0.01, 'N5': 0.06}
    }
}

COMPONENT_REPAIR_COST = 2  # Repair cost per damaged component

COMPONENT_REPAIR_TIME = 1/3  # 每个部件维修时间，单位：小时 (20分钟)

ABILITIES = ['N1', 'N2', 'N3', 'N4', 'N5']


TRAPEZOID = {'top': 12.0, 'bottom': 50.0, 'height': 32.0}  # 地图梯形参数 

TEAM_TYPE_NAMES = {
    'B1': '维修小队B1',
    'B2': '维修小队B2',
    'B3': '维修小队B3',
    'B4': '维修小队B4',
    'B5': '维修小队B5',
} 

STATE_COLOR_MAP = {
    '完好': '#4caf50',
    '轻微损伤': '#ffeb3b',
    '中度损伤': '#ff9800',
    '严重损伤': '#f44336',
}
EQUIPMENT_TYPE_COLORS = {
    'Z1': '#007bff',
    'Z2': '#4caf50',
    'Z3': '#ffeb3b',
    'Z4': '#ff9800',
    'Z5': '#f44336',
} 

SERVER_INSTANCE_NUM = 3  # 默认实例数量
SERVER_PORT_RANGE = (5000, 6000)  # 端口范围 