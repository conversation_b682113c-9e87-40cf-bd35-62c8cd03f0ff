import math
import time
import heapq
from models.kmeans import weighted_kmeans_clustering
from config import EQUIPMENT_TYPES, TEAM_CAPACITY, TEAM_SPEED, COMPONENT_REPAIR_COST
from models.pathfinding import calc_weighted_path
from models.utils import distance
from copy import deepcopy
from collections import defaultdict
from config import ABILITIES, CONTRIBUTION_TABLE
import numpy as np
from scipy.optimize import linear_sum_assignment

def optimal_camp_assignment(equipments, threats, P, trapezoid_params, teams_result, priority_list, component_repair_time=1/3):
    """
    使用加权K-means结合A*路径规划的集中营选址与小队分配算法
    
    参数:
    - equipments: 维修装备列表
    - threats: 威胁点列表
    - P: 集中营数量
    - trapezoid_params: 梯形地图参数
    - teams_result: 维修小队计算结果
    
    返回:
    - camps: 优化后的集中营位置列表
    - camp_assignments: 小队分配结果
    """
    from models.pathfinding import clear_path_cache, get_cache_stats
    clear_path_cache()
    # 1. 过滤需要维修的装备
    # 从config导入阈值比例
    from config import ABILITY_THRESHOLD_RATIO
    
    # 如果阈值比例小于1.0，只修复calculate_repair_teams选择的装备
    if ABILITY_THRESHOLD_RATIO < 1.0 and 'repaired_equipment_ids' in teams_result:
        repaired_ids = set(teams_result['repaired_equipment_ids'])
        repair_equipments = [e for e in equipments if e['id'] in repaired_ids]
        print(f"注意: 根据能力阈值{ABILITY_THRESHOLD_RATIO*100:.0f}%，只修复{len(repair_equipments)}个装备")
    else:
        # 否则修复所有损坏的装备
        repair_equipments = [e for e in equipments if e.get('state') != '完好' and e.get('repair_cost', 0) > 0]
    
    if not repair_equipments:
        print("警告: 没有需要维修的装备")
        return [], [], [], 0, [], {}, []
    
    # 2. 加权K-means生成集中营位置
    coordinates = [(e['x'], e['y']) for e in repair_equipments]
    weights = [e['repair_cost'] for e in repair_equipments]
    camps = weighted_kmeans_clustering(coordinates, weights, P, trapezoid_params, repair_equipments, threats)
    # 3. 按类型分组装备
    type_groups = {typ: [e for e in repair_equipments if e['type'] == typ] for typ in EQUIPMENT_TYPES}

    # --- 基于最小时间成本的小队分配 ---
    camp_team_counts = {c['id']: {typ: 0 for typ in EQUIPMENT_TYPES} for c in camps}
    camp_id_list = [c['id'] for c in camps]
    camp_id_to_camp = {c['id']: c for c in camps}
    
    # 打印调试信息
    print(f"DEBUG - 维修小队总数:")
    for typ in EQUIPMENT_TYPES:
        print(f"  {typ}: {teams_result['teams'][typ]}")
    
    # 检查是否需要强制分配维修小队
    # 从config导入阈值比例
    from config import ABILITY_THRESHOLD_RATIO
    
    # 如果阈值比例小于1.0，表示不需要修复所有装备
    if ABILITY_THRESHOLD_RATIO < 1.0:
        print(f"注意: 能力阈值设置为{ABILITY_THRESHOLD_RATIO*100:.0f}%，不需要修复所有装备")
        print(f"      只修复能力贡献最大的部分装备，以达到阈值")
        # 不强制分配小队，使用calculate_repair_teams的结果
    else:
        # 阈值比例为1.0，需要修复所有装备
        # 检查是否有需要维修但在calculate_repair_teams中未被考虑的装备
        for typ in EQUIPMENT_TYPES:
            if teams_result['teams'][typ] == 0 and len(type_groups[typ]) > 0:
                # 如果有该类型的损坏装备但没有分配维修小队，至少分配一个
                print(f"警告: {typ}类型有{len(type_groups[typ])}个需要维修的装备，但未分配维修小队，强制分配1个")
                
                # 计算该类型装备的总损坏部件数
                damaged_parts = sum(len(eq['damaged_components']) for eq in type_groups[typ])
                
                # 计算需要的小队数量（每个小队容量为TEAM_CAPACITY，每个部件修复成本为COMPONENT_REPAIR_COST）
                needed_teams = max(1, (damaged_parts * COMPONENT_REPAIR_COST + TEAM_CAPACITY - 1) // TEAM_CAPACITY)
                
                print(f"      该类型有{damaged_parts}个损坏部件，需要{needed_teams}个小队")
                teams_result['teams'][typ] = needed_teams
                teams_result['total_teams'] += needed_teams
    
    for typ in EQUIPMENT_TYPES:
        num_teams = teams_result['teams'][typ]
        eqs = type_groups[typ]
        if not eqs or num_teams == 0:
            continue
            
        if P == 1:
            # 只有一个集中营，所有小队都分配到它
            camp_id = camps[0]['id']
            camp_team_counts[camp_id][typ] = num_teams
            continue
            
        # 先将装备均分为num_teams组（按修复成本均衡分组）
        eqs_per_team = [[] for _ in range(num_teams)]
        # 按修复成本排序，确保分组更均衡
        sorted_eqs = sorted(eqs, key=lambda x: x.get('repair_cost', 0), reverse=True)

        # 使用贪心算法分组，每次将装备分配给当前总成本最小的组
        team_costs = [0] * num_teams
        for eq in sorted_eqs:
            min_cost_team = min(range(num_teams), key=lambda i: team_costs[i])
            eqs_per_team[min_cost_team].append(eq)
            team_costs[min_cost_team] += eq.get('repair_cost', 0)

        # 构建成本矩阵（使用float类型保持精度）
        cost_matrix = np.zeros((num_teams, P), dtype=float)
        
        # 填充真实小队-真实集中营的cost
        for team_idx in range(num_teams):
            for camp_idx, camp in enumerate(camps):
                total_time = 0.0
                current_pos = camp
                eqs_this_team = eqs_per_team[team_idx]
                eqs_left = eqs_this_team[:]
                while eqs_left:
                    min_dist = float('inf')
                    best_eq = None
                    for eq in eqs_left:
                        dist, move_time = calc_weighted_path(current_pos, eq, threats)
                        if dist < min_dist:
                            min_dist = dist
                            best_eq = eq
                    if not best_eq:
                        break
                    dist, move_time = calc_weighted_path(current_pos, best_eq, threats)
                    total_time += move_time
                    current_pos = best_eq
                    eqs_left.remove(best_eq)
                cost_matrix[team_idx, camp_idx] = total_time
        
        # 匈牙利算法求解最优分配
        row_ind, col_ind = linear_sum_assignment(cost_matrix)
        
        # 确保所有小队都被分配
        assigned_teams = 0
        for team_idx, camp_idx in zip(row_ind, col_ind):
            if team_idx < num_teams:  # 只统计真实小队
                camp_id = camps[camp_idx]['id']
                camp_team_counts[camp_id][typ] += 1
                assigned_teams += 1
                print(f"  分配: {typ}类型小队{team_idx}分配到集中营{camp_id}")
        
        # 如果有未分配的小队，强制分配到最近的集中营
        if assigned_teams < num_teams:
            print(f"警告: {typ}类型有{num_teams - assigned_teams}个小队未分配，进行强制分配")
            
            # 找出未分配的小队
            assigned_team_indices = set(row_ind)
            unassigned_team_indices = [i for i in range(num_teams) if i not in assigned_team_indices]
            
            for team_idx in unassigned_team_indices:
                # 找到成本最小的集中营
                camp_costs = [(camp_idx, cost_matrix[team_idx, camp_idx]) for camp_idx in range(P)]
                camp_costs.sort(key=lambda x: x[1])  # 按成本排序
                
                best_camp_idx = camp_costs[0][0]
                camp_id = camps[best_camp_idx]['id']
                camp_team_counts[camp_id][typ] += 1
                print(f"  强制分配: {typ}类型小队{team_idx}分配到集中营{camp_id}")
    
    # --- 分配校验 ---
    print("DEBUG - 小队分配校验:")
    for typ in EQUIPMENT_TYPES:
        assigned = sum(camp_team_counts[cid][typ] for cid in camp_team_counts)
        should = teams_result['teams'][typ]
        if assigned != should:
            print(f"  分配校验失败：{typ} 需求{should} 实际分配{assigned}")
        else:
            print(f"  分配校验通过：{typ} 需求{should} 实际分配{assigned}")
    
    # --- 基于最小化时间成本的装备分配与小队规划 ---
    camp_assignments = []
    all_paths = []
    total_time = 0.0
    
    # 创建装备到集中营和小队的映射
    equipment_to_team = {}  # 装备ID -> (集中营ID, 小队ID)
    
    # 为每个集中营创建分配记录
    for c in camps:
        camp_id = c['id']
        team_count = {typ: camp_team_counts[camp_id][typ] for typ in EQUIPMENT_TYPES}
        camp_assignments.append({
            'camp_id': camp_id,
            'camp_name': c['name'],
            'x': c['x'],
            'y': c['y'],
            'team_count': team_count
        })
    
    # 按类型分配小队和装备
    for typ in EQUIPMENT_TYPES:
        eqs = type_groups[typ]
        num_teams = teams_result['teams'][typ]
        
        if not eqs or num_teams == 0:
            continue
        
        # 创建小队列表，记录每个小队的集中营位置
        teams = []
        for camp_id, count in camp_team_counts.items():
            camp = camp_id_to_camp[camp_id]
            for i in range(count[typ]):
                teams.append({
                    'team_id': len(teams) + 1,
                    'type': typ,
                    'camp_id': camp_id,
                    'camp': camp,
                    'equipments': []
                })
        
        if not teams:
            continue
        
        # 计算每个小队到每个装备的时间成本
        cost_matrix = np.zeros((len(teams), len(eqs)), dtype=float)
        
        for team_idx, team in enumerate(teams):
            camp = team['camp']
            for eq_idx, eq in enumerate(eqs):
                dist, move_time = calc_weighted_path(camp, eq, threats)
                cost_matrix[team_idx, eq_idx] = move_time
        
        # 使用匈牙利算法进行最优分配
        if len(teams) >= len(eqs):
            # 小队数量 >= 装备数量，每个装备都能分配到小队
            row_ind, col_ind = linear_sum_assignment(cost_matrix)

            # 记录分配结果
            for team_idx, eq_idx in zip(row_ind, col_ind):
                if team_idx < len(teams) and eq_idx < len(eqs):
                    team = teams[team_idx]
                    eq = eqs[eq_idx]
                    team['equipments'].append(eq)
                    equipment_to_team[eq['id']] = (team['camp_id'], team['team_id'])
        else:
            # 小队数量 < 装备数量，需要多个装备分配给同一个小队
            # 转置成本矩阵，让装备选择小队
            transposed_cost_matrix = cost_matrix.T
            row_ind, col_ind = linear_sum_assignment(transposed_cost_matrix)

            # 记录分配结果（注意索引对应关系）
            for eq_idx, team_idx in zip(row_ind, col_ind):
                if eq_idx < len(eqs) and team_idx < len(teams):
                    team = teams[team_idx]
                    eq = eqs[eq_idx]
                    team['equipments'].append(eq)
                    equipment_to_team[eq['id']] = (team['camp_id'], team['team_id'])
        
        # 生成路径
        for team in teams:
            if not team['equipments']:
                continue
            
            camp = team['camp']
            team_eqs = team['equipments']
            
            # 规划小队路径
            team_paths, team_time = plan_team_path(camp, team_eqs, threats)
            total_time += team_time
            
            # 添加到总路径列表
            all_paths.extend(team_paths)
            
            print(f"集中营{team['camp_id']}的{typ}类型小队{team['team_id']}分配了{len(team_eqs)}个装备")
    
    # 检查是否有未分配的需维修装备，并强制分配
    unassigned_equipments = [e for e in repair_equipments if e['id'] not in equipment_to_team]
    if unassigned_equipments:
        print(f"警告: 有{len(unassigned_equipments)}个需要维修的装备未被分配到任何小队，进行强制分配")
        
        # 为每个未分配的装备找到最佳小队
        for eq in unassigned_equipments:
            min_time = float('inf')
            best_camp = None
            best_team_id = None
            
            # 找到对应类型的小队
            for camp_id, counts in camp_team_counts.items():
                if counts[eq['type']] > 0:
                    camp = camp_id_to_camp[camp_id]
                    dist, move_time = calc_weighted_path(camp, eq, threats)
                    if move_time < min_time:
                        min_time = move_time
                        best_camp = camp
                        best_team_id = 1  # 简化处理，分配给第一个小队
            
            # 如果没有找到对应类型的小队，尝试其他类型
            if not best_camp:
                for camp_id, counts in camp_team_counts.items():
                    if sum(counts.values()) > 0:
                        camp = camp_id_to_camp[camp_id]
                        dist, move_time = calc_weighted_path(camp, eq, threats)
                        if move_time < min_time:
                            min_time = move_time
                            best_camp = camp
                            # 找到第一个有小队的类型
                            for typ, count in counts.items():
                                if count > 0:
                                    best_team_id = 1
                                    break
            
            if best_camp:
                print(f"  强制分配: 装备{eq['id']}分配到集中营{best_camp['id']}的小队{best_team_id}")
                dist, move_time = calc_weighted_path(best_camp, eq, threats)
                path = {
                    'equipment_id': eq['id'],
                    'equipment_type': eq['type'],
                    'equipment_state': eq['state'],
                    'camp_id': best_camp['id'],
                    'camp_name': best_camp['name'],
                    'team_id': best_team_id,
                    'camp_x': best_camp['x'],
                    'camp_y': best_camp['y'],
                    'equipment_x': eq['x'],
                    'equipment_y': eq['y'],
                    'distance': round(dist, 3),
                    'time': round(move_time, 2)  # 时间单位：小时
                }
                all_paths.append(path)
                equipment_to_team[eq['id']] = (best_camp['id'], best_team_id)
                total_time += move_time
    
    # --- 计算总移动时间 ---
    # 总时间已经在分配过程中累加，不需要重新计算
    actual_total_time = total_time
    
    # --- 计算能力动态变化 ---
    # 计算完好状态总能力和基准值
    perfect_totals = {ab: 0.0 for ab in ABILITIES}
    for eq in equipments:
        for comp in CONTRIBUTION_TABLE[eq['type']]:
            for ab, contrib in CONTRIBUTION_TABLE[eq['type']][comp].items():
                perfect_totals[ab] += contrib
    
    # 从config导入阈值比例
    from config import ABILITY_THRESHOLD_RATIO
    
    # 设定基准值（完好状态的阈值比例）
    benchmarks = {ab: val * ABILITY_THRESHOLD_RATIO for ab, val in perfect_totals.items()}
    
    # 计算当前能力值 - 这是初始状态
    initial_abilities = {ab: sum(eq['abilities'][ab] for eq in equipments) for ab in ABILITIES}
    
    print(f"DEBUG - 完好状态总能力: {perfect_totals}")
    print(f"DEBUG - 基准值 ({int(ABILITY_THRESHOLD_RATIO*100)}%): {benchmarks}")
    print(f"DEBUG - 当前总能力: {initial_abilities}")
    
    # 生成小队维修过程
    team_processes = generate_team_repair_process(equipments, threats, camps, camp_team_counts, priority_list, component_repair_time, all_paths)
    
    # --- 从小队维修过程生成repair_events，确保时间一致 ---
    repair_events = []
    
    # 遍历每个小队维修过程
    for team in team_processes:
        for step in team.get('steps', []):
            eq_id = step.get('equipment_id')
            # 找到对应的装备
            for eq in equipments:
                if eq['id'] == eq_id:
                    # 获取装备索引
                    eq_idx = equipments.index(eq)
                    # 获取部件列表
                    components = eq.get('damaged_components', [])
                    # 计算完成时间
                    complete_time = step.get('total_time', 0)
                    # 为每个部件添加修复事件
                    for comp in components:
                        repair_events.append((complete_time, eq_idx, comp, eq['type']))
                    break
    
    # 按时间排序修复事件
    repair_events.sort(key=lambda e: e[0])
    
    # 检查最大修复时间是否合理
    max_complete_time = 0
    if repair_events:
        max_repair_time = repair_events[-1][0]
        if max_repair_time > 24:  # 如果超过24小时，可能有问题
            print(f"警告: 最大修复时间 {max_repair_time:.2f} 小时可能不合理，检查时间单位")
            # 尝试修正：如果时间单位错误（可能是分钟而非小时），进行转换
            if max_repair_time > 50:  # 如果超过50小时，可能是分钟单位
                print("尝试将时间单位从分钟转换为小时...")
                repair_events = [(t/60, idx, comp, eq_type) for t, idx, comp, eq_type in repair_events]
                print(f"修正后最后一个维修事件时间: {repair_events[-1][0]:.2f}小时")
    
    # 构建timeline - 从初始能力值开始
    timeline = [{'time': 0, **initial_abilities}]
    
    # 使用初始能力值作为起点，确保不会出现能力值下降
    current = deepcopy(initial_abilities)
    
    for complete_time, idx, comp, eq_type in repair_events:
        for ab, contrib in CONTRIBUTION_TABLE[eq_type][comp].items():
            current[ab] += contrib
        timeline.append({'time': round(complete_time, 2), **deepcopy(current)})  # 时间单位：小时，保留2位小数
        max_complete_time = max(max_complete_time, complete_time)
    
    print(f"DEBUG - 能力动态变化图最大时间: {max_complete_time:.2f}小时")
    print(f"DEBUG - 维修事件总数: {len(repair_events)}")
    if repair_events:
        print(f"DEBUG - 最后一个维修事件时间: {repair_events[-1][0]:.2f}小时")
    
    # 检查小队维修过程和能力动态变化图时间是否一致
    max_team_time = max([team['total_time'] for team in team_processes]) if team_processes else 0
    print(f"DEBUG - 小队维修过程最大时间: {max_team_time:.2f}小时")
    
    # 如果时间差异过大，进行修正
    if abs(max_complete_time - max_team_time) > 0.1 and max_complete_time > 0 and max_team_time > 0:
        print(f"警告: 能力动态变化图时间({max_complete_time:.2f}小时)与小队维修过程时间({max_team_time:.2f}小时)不一致，进行修正")
        
        # 确定时间比例
        time_ratio = max_team_time / max_complete_time
        
        # 重新生成timeline
        if abs(time_ratio - 1.0) > 0.01:  # 如果差异超过1%
            print(f"应用时间比例: {time_ratio:.4f}")
            timeline_new = [{'time': 0, **timeline[0]}]
            for i in range(1, len(timeline)):
                new_time = timeline[i]['time'] * time_ratio
                timeline_new.append({'time': round(new_time, 2), **{k: v for k, v in timeline[i].items() if k != 'time'}})
            timeline = timeline_new
            print(f"修正后能力动态变化图最大时间: {timeline[-1]['time']:.2f}小时")
    
    # 输出缓存统计信息
    cache_stats = get_cache_stats()
    print(f"路径规划统计：缓存{cache_stats['cache_size']}条路径，总计算{cache_stats['total_paths']}条，平均每条{cache_stats['avg_time_per_path']:.4f}秒")
    
    # Return also timeline, benchmarks, team_processes
    return camps, camp_assignments, all_paths, round(actual_total_time, 2), timeline, benchmarks, team_processes  # 总时间单位：小时

def plan_team_path(camp, eqs, threats):
    """
    规划小队路径，考虑装备优先级
    
    参数:
    - camp: 集中营
    - eqs: 装备列表
    - threats: 威胁点列表
    
    返回:
    - paths: 路径列表
    - total_time: 总时间
    """
    if not eqs:
        return [], 0
    
    # 从main.py获取全局优先级列表（如果可用）
    global_priority_list = None
    try:
        from flask import current_app
        if current_app:
            global_priority_list = current_app.config.get('CURRENT_PRIORITY_LIST')
    except:
        pass
    
    # 如果无法获取全局优先级，使用默认顺序
    if not global_priority_list:
        global_priority_list = ['N1', 'N2', 'N3', 'N4', 'N5']
    
    # 计算每个装备的优先级得分
    eq_priorities = {}
    for eq in eqs:
        priority_score = 0
        # 根据损坏部件对优先级能力的贡献计算优先级
        for comp in eq.get('damaged_components', []):
            # 对每个部件，按优先级顺序加权累加其对各能力的贡献
            for i, ability in enumerate(global_priority_list):
                # 优先级权重：第一优先级权重为5，第二为4，依此类推
                priority_weight = len(global_priority_list) - i
                contrib = CONTRIBUTION_TABLE[eq['type']].get(comp, {}).get(ability, 0)
                priority_score += contrib * priority_weight
        
        # 如果没有损坏部件信息，则根据damage_level或state估算
        if not eq.get('damaged_components') and eq.get('damage_level'):
            priority_score = eq['damage_level']
        elif not eq.get('damaged_components') and eq.get('state'):
            # 根据状态文本估算优先级
            if eq['state'] == '严重损伤':
                priority_score = 3
            elif eq['state'] == '中度损伤':
                priority_score = 2
            elif eq['state'] == '轻微损伤':
                priority_score = 1
        
        eq_priorities[eq['id']] = priority_score
    
    # 将装备分为高、中、低三个优先级组
    high_priority = []
    medium_priority = []
    low_priority = []
    
    # 如果有优先级得分，按得分排序
    if eq_priorities:
        # 计算优先级阈值
        scores = list(eq_priorities.values())
        if scores:
            max_score = max(scores)
            high_threshold = max_score * 0.7  # 高优先级阈值
            medium_threshold = max_score * 0.3  # 中优先级阈值
            
            for eq in eqs:
                score = eq_priorities[eq['id']]
                if score >= high_threshold:
                    high_priority.append(eq)
                elif score >= medium_threshold:
                    medium_priority.append(eq)
                else:
                    low_priority.append(eq)
    
    # 如果没有有效的优先级信息，所有装备都放入中优先级
    if not high_priority and not medium_priority and not low_priority:
        medium_priority = eqs
    
    # 按优先级分段进行贪心最近邻路径规划
    paths = []
    total_time = 0
    current_pos = camp
    
    # 依次处理高、中、低优先级组
    for priority_group in [high_priority, medium_priority, low_priority]:
        if not priority_group:
            continue
        
        # 在当前优先级组内使用贪心最近邻
        remaining_eqs = priority_group.copy()
        while remaining_eqs:
            min_dist = float('inf')
            best_eq = None
            for eq in remaining_eqs:
                dist, move_time = calc_weighted_path(current_pos, eq, threats)
                if dist < min_dist:
                    min_dist = dist
                    best_eq = eq
            
            if not best_eq:
                break
            
            # 计算从当前位置到最近装备的时间
            dist, move_time = calc_weighted_path(current_pos, best_eq, threats)
            total_time += move_time
            
            # 添加路径
            paths.append({
                'equipment_id': best_eq['id'],
                'equipment_type': best_eq['type'],
                'equipment_state': best_eq['state'],
                'camp_id': camp['id'],
                'camp_name': camp['name'],
                'camp_x': current_pos['x'] if 'name' not in current_pos else camp['x'],
                'camp_y': current_pos['y'] if 'name' not in current_pos else camp['y'],
                'equipment_x': best_eq['x'],
                'equipment_y': best_eq['y'],
                'distance': round(dist, 3),
                'time': round(move_time, 2)  # 时间单位：小时
            })
            
            # 更新当前位置并从待访问列表中移除
            current_pos = best_eq
            remaining_eqs.remove(best_eq)
    
    return paths, round(total_time, 2)

def adjust_team_allocation(camp_costs, camp_team_counts, total_teams, equipment_type, key_map):
    current_total = sum(camp_team_counts[cid][equipment_type] for cid in camp_team_counts)
    
    while current_total > total_teams:
        camps_by_cost = sorted(
            [(cid, camp_costs[cid][equipment_type]) for cid in camp_team_counts if camp_team_counts[cid][equipment_type] > 0],
            key=lambda x: x[1]
        )
        if not camps_by_cost:
            break
        camp_id = camps_by_cost[0][0]
        camp_team_counts[camp_id][equipment_type] -= 1
        current_total -= 1
    
    while current_total < total_teams:
        camps_by_cost = sorted(
            [(cid, camp_costs[cid][equipment_type]) for cid in camp_team_counts],
            key=lambda x: x[1], reverse=True
        )
        if not camps_by_cost:
            break
        camp_id = camps_by_cost[0][0]
        camp_team_counts[camp_id][equipment_type] += 1
        current_total += 1

def ensure_minimal_team_allocation(camp_equipments, camp_team_counts, equipment_type):
    for camp_id, eq_list in camp_equipments.items():
        if eq_list and camp_team_counts[camp_id][equipment_type] == 0:
            donor_camp = None
            for other_id in camp_team_counts:
                if other_id != camp_id and camp_team_counts[other_id][equipment_type] > 1:
                    donor_camp = other_id
                    break
            if donor_camp:
                camp_team_counts[donor_camp][equipment_type] -= 1
                camp_team_counts[camp_id][equipment_type] += 1 

def generate_team_repair_process(equipments, threats, camps, camp_team_counts, priority_list, component_repair_time=1/3, all_paths=[]):
    """
    生成小队维修过程的详细信息
    
    参数:
    - equipments: 维修装备列表
    - threats: 威胁点列表
    - camps: 集中营列表
    - camp_team_counts: 集中营小队分配情况
    - priority_list: 能力优先级列表
    - component_repair_time: 部件维修时间（小时）
    - all_paths: 装备分配路径
    
    返回:
    - team_processes: 每个小队的维修过程列表
    """
    from config import TEAM_TYPE_NAMES, COMPONENT_NAMES
    
    team_processes = []
    
    # 创建装备ID到集中营和小队ID的映射
    equipment_to_team = {}  # 装备ID -> (集中营ID, 小队ID)
    for path in all_paths:
        if 'equipment_id' in path and 'camp_id' in path:
            team_id = path.get('team_id', 1)  # 默认为1
            equipment_to_team[path['equipment_id']] = (path['camp_id'], team_id)
    
    # 按集中营和小队分组装备
    camp_team_equipments = {}  # {camp_id: {team_id: {type: [equipments]}}}
    
    for eq in equipments:
        if eq['id'] in equipment_to_team:
            camp_id, team_id = equipment_to_team[eq['id']]
            
            if camp_id not in camp_team_equipments:
                camp_team_equipments[camp_id] = {}
                
            if team_id not in camp_team_equipments[camp_id]:
                camp_team_equipments[camp_id][team_id] = {}
                
            eq_type = eq['type']
            if eq_type not in camp_team_equipments[camp_id][team_id]:
                camp_team_equipments[camp_id][team_id][eq_type] = []
                
            camp_team_equipments[camp_id][team_id][eq_type].append(eq)
    
    # 获取全局优先级列表
    global_priority_list = None
    try:
        from flask import current_app
        if current_app:
            global_priority_list = current_app.config.get('CURRENT_PRIORITY_LIST')
    except:
        pass
    if not global_priority_list:
        global_priority_list = priority_list
    
    # 记录最大维修时间，用于检查
    max_repair_time = 0
    team_id_counter = 1
    
    # 遍历每个集中营
    for camp_id, team_data in camp_team_equipments.items():
        camp = next(c for c in camps if c['id'] == camp_id)
        
        # 遍历该集中营的每个小队
        for team_id, type_eqs in team_data.items():
            # 遍历该小队负责的每种类型的装备
            for typ, eqs in type_eqs.items():
                if not eqs:
                    continue
                
                # 收集该类型装备的所有损坏部件，按优先级排序
                bad_comps = []
                for eq in eqs:
                    idx = equipments.index(eq)
                    for comp in eq['damaged_components']:
                        gains = {ab: CONTRIBUTION_TABLE[eq['type']][comp].get(ab, 0.0) for ab in global_priority_list}
                        bad_comps.append((idx, comp, eq['type'], gains, eq))
                
                # 按优先级排序
                bad_comps.sort(key=lambda x: tuple(-x[3].get(ab, 0) for ab in global_priority_list))
                
                # 计算装备优先级得分
                eq_priorities = {}
                for eq in eqs:
                    priority_score = 0
                    for comp in eq.get('damaged_components', []):
                        for i, ability in enumerate(global_priority_list):
                            priority_weight = len(global_priority_list) - i
                            contrib = CONTRIBUTION_TABLE[eq['type']].get(comp, {}).get(ability, 0)
                            priority_score += contrib * priority_weight
                    eq_priorities[eq['id']] = priority_score
                
                # 按优先级排序装备
                sorted_eqs = sorted(eqs, key=lambda eq: eq_priorities.get(eq['id'], 0), reverse=True)
                
                # 规划小队路径（贪心最近邻）
                current_pos = camp
                remaining_eqs = sorted_eqs[:]
                team_steps = []
                current_time = 0.0
                
                while remaining_eqs:
                    # 找最近的装备
                    min_dist = float('inf')
                    best_eq = None
                    for eq in remaining_eqs:
                        # 处理current_pos可能是装备对象的情况
                        if 'name' not in current_pos:
                            # current_pos是装备对象，需要转换为camp格式
                            current_camp = {'x': current_pos['x'], 'y': current_pos['y']}
                        else:
                            # current_pos是camp对象
                            current_camp = current_pos
                        
                        dist, _ = calc_weighted_path(current_camp, eq, threats)
                        if dist < min_dist:
                            min_dist = dist
                            best_eq = eq
                    
                    if not best_eq:
                        break
                    
                    # 计算移动时间
                    if 'name' not in current_pos:
                        current_camp = {'x': current_pos['x'], 'y': current_pos['y']}
                    else:
                        current_camp = current_pos
                    
                    dist, move_time = calc_weighted_path(current_camp, best_eq, threats)
                    
                    # 对于装备4，添加调试信息
                    if best_eq['id'] == 4 and best_eq['type'] == 'Z5':
                        print(f"调试信息 - 装备4(Z5)移动时间计算:")
                        print(f"  从位置: ({current_camp['x']:.2f}, {current_camp['y']:.2f})")
                        print(f"  到位置: ({best_eq['x']:.2f}, {best_eq['y']:.2f})")
                        print(f"  距离: {dist:.2f}")
                        print(f"  移动时间(小时): {move_time:.4f}")
                        print(f"  移动时间(分钟): {move_time*60:.1f}")
                        print(f"  当前累计时间: {current_time:.4f}")
                    
                    current_time += move_time
                    current_time = round(current_time, 2)  # 每次添加移动时间后进行舍入
                    
                    # 获取该装备需要维修的部件
                    eq_comps = []
                    for bc in bad_comps:
                        if bc[4]['id'] == best_eq['id']:
                            eq_comps.append((bc[0], bc[1], bc[2]))
                    
                    # 计算维修时间 - 确保与repair_events使用相同的计算逻辑
                    repair_time = len(eq_comps) * component_repair_time
                    current_time += repair_time
                    current_time = round(current_time, 2)  # 每次添加维修时间后进行舍入
                    
                    # 更新最大维修时间
                    max_repair_time = max(max_repair_time, current_time)
                    
                    # 添加维修步骤
                    team_steps.append({
                        'team_id': team_id_counter,
                        'team_type': TEAM_TYPE_NAMES.get(typ, typ),
                        'camp_id': camp_id,
                        'camp_name': camp['name'],
                        'equipment_id': best_eq['id'],
                        'equipment_type': best_eq['type'],
                        'equipment_state': best_eq.get('state', ''),
                        'components': [COMPONENT_NAMES.get(comp, comp) for _, comp, _ in eq_comps],
                        'move_time': round(move_time, 2),
                        'repair_time': round(repair_time, 2),
                        'total_time': round(current_time, 2),
                        'from_x': current_camp['x'],
                        'from_y': current_camp['y'],
                        'to_x': best_eq['x'],
                        'to_y': best_eq['y']
                    })
                    
                    current_pos = best_eq
                    remaining_eqs.remove(best_eq)
                
                if team_steps:
                    team_processes.append({
                        'team_id': team_id_counter,
                        'team_type': TEAM_TYPE_NAMES.get(typ, typ),
                        'camp_id': camp_id,
                        'camp_name': camp['name'],
                        'steps': team_steps,
                        'total_time': round(current_time, 2)
                    })
                    team_id_counter += 1
    
    # 检查最大维修时间是否合理
    if max_repair_time > 24:  # 如果超过24小时，可能有问题
        print(f"警告: 小队维修过程最大时间 {max_repair_time:.2f} 小时可能不合理，检查时间单位")
        # 尝试修正：如果时间单位错误（可能是分钟而非小时），进行转换
        if max_repair_time > 50:  # 如果超过50小时，可能是分钟单位
            print("尝试将小队维修过程时间单位从分钟转换为小时...")
            for process in team_processes:
                process['total_time'] /= 60
                for step in process['steps']:
                    step['move_time'] /= 60
                    step['repair_time'] /= 60
                    step['total_time'] /= 60
            max_repair_time /= 60
    
    print(f"DEBUG - 小队维修过程生成完成，共{len(team_processes)}个小队，最大维修时间: {max_repair_time:.2f}小时")
    return team_processes 