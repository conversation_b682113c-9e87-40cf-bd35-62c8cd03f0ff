from config import EQUIPMENT_TYPES
import math

def generate_camps(P, equipment_set, threat_set, trapezoid_random_point_func):
    camps = []
    camp_set = set()
    while len(camps) < P:
        pt = trapezoid_random_point_func()
        key = (round(pt[0], 4), round(pt[1], 4))
        if key not in equipment_set and key not in threat_set and key not in camp_set:
            camps.append({
                'id': len(camps)+1,
                'name': f'集中营{str(len(camps)+1).zfill(2)}',
                'x': pt[0],
                'y': pt[1]
            })
            camp_set.add(key)
    return camps

import time
from models.pathfinding import calc_weighted_path

def calculate_camp_paths(camps, equipments, threats, speed):
    path_calc_start_time = time.time()
    
    paths = []
    total_time = 0.0
    
    camp_stats = {camp['id']: {typ: {'total_cost': 0, 'equipments': []} for typ in EQUIPMENT_TYPES} for camp in camps}
    
    for e in equipments:
        # 跳过完好装备
        if e.get('state') == '完好' or e.get('repair_cost', 0) == 0:
            continue
        min_dist = float('inf')
        best_camp = None
        for c in camps:
            weighted_dist, _ = calc_weighted_path(c, e, threats)
            if weighted_dist < min_dist:
                min_dist = weighted_dist
                best_camp = c
        
        if best_camp:
            camp_stats[best_camp['id']][e['type']]['total_cost'] += e['repair_cost']
            camp_stats[best_camp['id']][e['type']]['equipments'].append(e)
            
            weighted_dist, time_cost = calc_weighted_path(best_camp, e, threats)
            total_time += time_cost
            
            paths.append({
                'equipment_id': e['id'],
                'equipment_type': e['type'],
                'equipment_state': e['state'],
                'camp_id': best_camp['id'],
                'camp_name': best_camp['name'],
                'camp_x': best_camp['x'],
                'camp_y': best_camp['y'],
                'equipment_x': e['x'],
                'equipment_y': e['y'],
                'distance': round(weighted_dist, 3),
                'time': round(time_cost, 2),  # 时间单位：小时
                'path': [
                    {'x': best_camp['x'], 'y': best_camp['y']},
                    {'x': e['x'], 'y': e['y']}
                ]
            })
    
    path_calc_end_time = time.time()
    path_calc_total_time = path_calc_end_time - path_calc_start_time
    print(f"路径计算完成，总计算时间: {path_calc_total_time:.4f}秒，计算了{len(paths)}条路径")
    
    return paths, round(total_time, 2), camp_stats  # 时间单位：小时 