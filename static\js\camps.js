// 显示集中营详细信息
function showCampDetail(camps) {
    const area = document.getElementById('campDetailArea');
    const table = document.getElementById('campDetailTable');
    if (table) table.innerHTML = '';
    camps.forEach((c, i) => {
        table.innerHTML += `<tr>
            <td>${c.id}</td>
            <td>${c.name}</td>
            <td>(${c.x.toFixed(2)}, ${c.y.toFixed(2)})</td>
        </tr>`;
    });
    area.style.display = 'block';
}

// 显示集中营分配明细
function showCampAssignDetail(assigns) {
    const area = document.getElementById('campAssignArea');
    const table = document.getElementById('campAssignTable');
    if (table) table.innerHTML = '';
    // 动态获取 teamTypes 和 teamTypeNames
    const teamTypes = window.TEAM_TYPES || ['B1', 'B2', 'B3', 'B4', 'B5'];
    const teamTypeNames = window.TEAM_TYPE_NAMES || {
        'B1': '维修小队B1',
        'B2': '维修小队B2',
        'B3': '维修小队B3',
        'B4': '维修小队B4',
        'B5': '维修小队B5',
    };
    // 确保有 thead
    let thead = null;
    if (table && table.parentNode) {
        thead = table.parentNode.querySelector('thead');
        if (!thead) {
            thead = document.createElement('thead');
            table.parentNode.insertBefore(thead, table);
        }
        let th = '<tr><th>序号</th><th>集中营名称</th>';
        teamTypes.forEach((b) => {
            th += `<th>${teamTypeNames[b]}</th>`;
        });
        th += '</tr>';
        thead.innerHTML = th;
    }
    assigns.forEach((c, i) => {
        let row = `<tr>
            <td>${i + 1}</td>
            <td>${c.camp_name}</td>`;
        teamTypes.forEach((b, idx) => {
            const typ = 'Z' + (idx + 1);
            row += `<td>${c.team_count[typ] || 0}</td>`;
        });
        row += '</tr>';
        table.innerHTML += row;
    });
    area.style.display = 'block';
}

// 显示路径与时间成本
function showPathDetail(paths, totalTime) {
    const area = document.getElementById('pathDetailArea');
    const table = document.getElementById('pathDetailTable');
    const total = document.getElementById('totalTimeCost');
    const unassignedStations = document.getElementById('unassignedStations');
    const unassignedCount = document.getElementById('unassignedCount');

    if (table) table.innerHTML = '';
    let unassignedCountValue = 0;
    paths.forEach((p, i) => {
        if (!p.camp_id) { // 未分配的维修站
            unassignedCountValue++;
            table.innerHTML += `<tr>
                <td>${p.equipment_id||i+1}</td>
                <td>${p.equipment_type}</td>
                <td>${p.equipment_state}</td>
                <td>未分配</td>
                <td>${p.distance}</td>
                <td>${p.time}</td>
            </tr>`;
        } else {
            table.innerHTML += `<tr>
                <td>${p.equipment_id||i+1}</td>
                <td>${p.equipment_type}</td>
                <td>${p.equipment_state}</td>
                <td>${p.camp_id}</td>
                <td>${p.distance}</td>
                <td>${p.time}</td>
            </tr>`;
        }
    });
    // 显示总时间（仅移动时间）
    total.textContent = formatTime(totalTime);
    if (unassignedStations) {
        unassignedStations.innerHTML = `⚠️ 注意：有<span id="unassignedCount">${unassignedCountValue}</span>个维修站未被分配，因为它们部件完好，无需维修`;
    }
    area.style.display = 'block';
}

// 格式化时间（小时）
function formatTime(hours) {
    if (hours < 1) {
        const minutes = Math.round(hours * 60);
        return `${minutes}分钟`;
    } else {
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        if (m > 0) {
            return `${h}小时${m}分钟`;
        } else {
            return `${h}小时`;
        }
    }
}

// 计算两点间距离
function distance(p1, p2) {
    return Math.hypot(p1.x - p2.x, p1.y - p2.y);
} 