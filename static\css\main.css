body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}
.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}
.header p {
    font-size: 1.1em;
    opacity: 0.9;
}
.content {
    padding: 40px;
}
.input-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}
.input-group {
    display: inline-block;
    margin: 0 20px;
}
label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #333;
}
input[type="number"] {
    padding: 12px 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    width: 150px;
    transition: border-color 0.3s;
}
input[type="number"]:focus {
    outline: none;
    border-color: #4facfe;
}
button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s;
    margin-left: 20px;
}
button:hover {
    transform: translateY(-2px);
}
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}
.result-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}
.result-card h3 {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #4facfe;
    padding-bottom: 10px;
}
.team-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}
.team-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}
.team-card h4 {
    margin-bottom: 10px;
    font-size: 1.2em;
}
.team-count {
    font-size: 2em;
    font-weight: bold;
}
.map-section {
    margin-top: 30px;
    text-align: center;
}
.map-legend {
    margin-top: 10px;
    text-align: left;
    display: inline-block;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.map-legend span {
    display: inline-block;
    margin-right: 18px;
    vertical-align: middle;
}
.legend-dot {
    display: inline-block;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
}
.legend-station { background: #007bff; border:2px solid #fff; }
.legend-threat { background: #ff0000; border:2px solid #fff; }
.legend-heat { background: linear-gradient(90deg, #fff 0%, #ffcccc 100%); width:40px; height:14px; border-radius:7px; margin-right:6px; }
.legend-threat-area { 
    display: inline-block;
    width: 40px; 
    height: 14px; 
    background: linear-gradient(90deg, rgba(255,0,0,0.7) 0%, rgba(255,0,0,0.5) 30%, rgba(255,0,0,0.2) 70%, rgba(255,0,0,0) 100%);
    border-radius: 7px; 
    margin-right: 6px;
    vertical-align: middle;
}
.loading, .error { text-align: center; margin-top: 20px; }
.error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb; }
.station-status-dot {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}
.status-light { background: #4caf50; }
.status-medium { background: #ff9800; }
.status-severe { background: #f44336; }
.station-typeA { border: 3px solid #007bff !important; }
.station-typeB { border: 3px solid #00bcd4 !important; }
.station-typeC { border: 3px solid #9c27b0 !important; }
.table-align th, .table-align td { text-align: center; padding: 8px; }
.table-align th { background: #f8f9fa; }
.table-align tr:nth-child(even) { background: #f4f6fa; }
.legend-status-dot { width:14px; height:14px; border-radius:50%; display:inline-block; margin-right:4px; }
.axis-label { font-size:12px; fill:#888; }
.station-triangle {
    width: 0; height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 18px solid #007bff;
    display: inline-block;
    vertical-align: middle;
    margin-right: 6px;
}
.camp-star {
    color: #fbc02d;
    font-size: 22px;
    margin-right: 4px;
    vertical-align: middle;
}
.toggle-btn {
    margin-left: 15px;
    font-size: 14px;
    padding: 6px 12px;
    background: #f1f3f5;
    color: #444;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.edit-btn, .delete-btn {
    padding: 4px 8px;
    margin: 0 2px;
    font-size: 12px;
    border-radius: 4px;
}
.edit-btn {
    background: #4facfe;
    color: white;
}
.delete-btn {
    background: #ff6b6b;
    color: white;
}
.coordinate-input {
    width: 60px;
    padding: 4px;
    margin: 0 2px;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.save-btn {
    background: #4caf50;
    color: white;
    padding: 4px 8px;
    margin: 0 2px;
    font-size: 12px;
    border-radius: 4px;
}
.cancel-btn {
    background: #9e9e9e;
    color: white;
    padding: 4px 8px;
    margin: 0 2px;
    font-size: 12px;
    border-radius: 4px;
}
.recalculate-btn {
    background: #2196f3;
    color: white;
    padding: 6px 12px;
    margin-left: 15px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
}
.recalculate-btn:hover {
    background: #0b7dda;
} 
.add-btn {
  padding: 2px 10px;
  font-size: 13px;
  border-radius: 4px;
  background: #4caf50;
  color: #fff;
  border: none;
  cursor: pointer;
  margin: 0 2px;
  transition: background 0.2s;
}
.add-btn:hover {
  background: #388e3c;
} 