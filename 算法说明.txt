集中营选址问题
基于加权K-means聚类，针对维修场景优化：
加权聚类：维修需求大的站点对集中营位置影响更大（需求=权重）。
虚拟点补充：若维修站数量不足，自动在周围生成虚拟辅助点（低权重），确保集中营均匀分布。
边界约束：集中营不会超出地图边界，且避开维修站和危险区域（微调位置）。
自动处理"空营地"：重新随机初始化空集群。
初始点优化：从实际维修站或虚拟点中随机选初始位置，减少随机性，提高效率。
收敛条件：当分配结果不再变化或达到最大迭代次数时停止。


维修小队分配到集中营（基于最小时间成本）
输入：各类型维修小队数量、集中营位置、装备位置
算法：匈牙利算法（最优分配算法）
优化：
先将装备按小队数量均分为多组
计算每组装备从每个集中营出发的总移动时间（贪心最近邻）
构建cost_matrix（行：小队，列：集中营，值：时间成本）
用匈牙利算法求解最小总时间成本的分配方案
特殊处理：
只有1个集中营时直接全部分配
允许部分集中营不分配小队（更优的全局分配）
确保所有真实小队都被分配到真实集中营
强制分配：未分配小队自动分配到最近集中营


装备筛选（基于能力阈值）
输入：所有损坏装备、能力优先级、能力阈值比例（ABILITY_THRESHOLD_RATIO）
算法：贪心算法
优化：
计算完好状态下的总能力值作为基准（100%）
根据能力阈值比例计算每种能力的目标阈值
按能力优先级和装备对能力的贡献排序装备
选择对优先能力贡献最大的装备进行修复，直到所有能力达到阈值
如设置阈值为80%，则只修复能使系统能力达到80%的最少装备


装备分配到小队（基于最小时间成本）
输入：各集中营的小队数量、装备位置、能力优先级
算法：匈牙利算法（最优分配算法）
优化：
计算每个小队到每个装备的移动时间成本
构建cost_matrix（行：小队，列：装备，值：时间成本）
用匈牙利算法求解最小总时间成本的分配方案
特殊处理：
确保每个装备只分配给一个小队
强制分配：未分配装备自动分配到最近的小队


维修任务规划（基于能力优先级）
输入：小队分配的装备列表，能力优先级列表
算法：贪心算法
优化：
根据能力阈值确定需要修复的装备（ABILITY_THRESHOLD_RATIO）
按能力优先级对装备部件排序（优先修复对高优先级能力贡献大的部件）
按优先级顺序规划维修路径，使系统能力尽快达到阈值
考虑小队容量限制（TEAM_CAPACITY）
阈值控制：当所有能力达到设定阈值时停止修复


小队维修路径规划（优先级分段贪心最近邻）
输入：小队分配的装备列表，能力优先级列表
算法：优先级分段贪心最近邻
优化：
根据装备部件对优先级能力的贡献计算装备优先级得分
按优先级得分对装备排序
使用贪心最近邻算法规划路径（从当前位置选择最近装备）
考虑威胁点影响（威胁区域移动成本高）


对威胁点的处理
A*路径规划避开威胁
优先修复对高优先级能力贡献大的装备，同时在同级装备间最小化移动时间
威胁区域移动成本增加（威胁点影响因子）


这里存在两部分的路径规划：
1. 宏观路径规划（装备访问顺序）
这一层使用的是优先级贪心最近邻算法，即：
按装备对优先级能力的贡献进行排序
在排序后的装备列表中使用贪心最近邻算法（选择最近的装备）
决定了小队访问装备的顺序


2. 微观路径规划（两点间具体路径）
A*路径规划算法，在从一个点移动到另一个点时（如从集中营到装备，或从一个装备到另一个装备）
使用A*算法计算具体的移动路径
考虑威胁点的影响，自动绕开高威胁区域
路径缓存优化：缓存已计算路径，避免重复计算
距离阈值优化：对于距离较远的点，直接使用直线距离


可视化与数据结构
装备-小队-集中营关系：
每个装备只分配给一个小队
每个小队只分配到一个集中营
可视化时只在装备和其所属小队的集中营之间连线
确保装备-小队-集中营的一对一对一关系，避免多重连接


关于全局最优的说明
逼近全局最优,尽量别说达到全局最优。
你可以在一个小点，实现全局最优，但是整个流程很多部分，你不能保证每一步都是全局最优。
此外，就算每一步都是全局最优  ≠  整体是全局最优