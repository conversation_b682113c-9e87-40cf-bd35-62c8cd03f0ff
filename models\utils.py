import random
import math
from config import TRAPEZOID

def trapezoid_random_point():
    """
    在梯形区域内生成随机点
    
    返回:
    - (x, y): 随机生成的点坐标
    """
    top, bottom, height = TRAPEZOID['top'], TRAPEZOID['bottom'], TRAPEZOID['height']
    
    # 随机生成y坐标
    y = random.uniform(0, height)
    
    # 根据y坐标计算当前高度处的宽度
    current_width = top + (bottom - top) * (y / height)
    
    # 随机生成x坐标
    half_width = current_width / 2
    x = random.uniform(-half_width, half_width)
    
    return x, y

def distance(p1, p2):
    """计算两点间的欧几里得距离"""
    return math.sqrt((p2[0] - p1[0]) ** 2 + (p2[1] - p1[1]) ** 2) 