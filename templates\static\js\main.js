// 计算并绘制
async function calculateAndDraw() {
    const M = document.getElementById('inputM').value;
    const K = document.getElementById('inputK').value;
    const P = document.getElementById('inputP').value;
    const btn = document.getElementById('mainBtn');
    const loading = document.getElementById('mainLoading');
    const error = document.getElementById('mainError');
    const resultArea = document.getElementById('resultArea');
    error.style.display = 'none';
    loading.style.display = 'block';
    btn.disabled = true;
    resultArea.style.display = 'none';
    if (!M || !K || !P || M<=0 || K<=0 || P<=0) {
        error.textContent = '请输入有效的M、K、P';
        error.style.display = 'block';
        loading.style.display = 'none';
        btn.disabled = false;
        return;
    }
    try {
        // 获取地图数据
        const mapRes = await fetch('/map_data', {
            method: 'POST',
            headers: {'Content-Type':'application/json'},
            body: JSON.stringify({M:parseInt(M), K:parseInt(K), P:parseInt(P)})
        });
        const mapData = await mapRes.json();
        if (!mapRes.ok) throw new Error(mapData.error||'地图生成失败');
        
        // 直接从mapData中提取维修小队统计数据
        const team_result = {
            teams_1: mapData.team_result.teams_1,
            teams_2: mapData.team_result.teams_2,
            teams_3: mapData.team_result.teams_3,
            total_teams: mapData.team_result.total_teams,
            stations_detail: mapData.team_result.stations_detail
        };
        
        // 显示维修小队结果
        showTeamSummary(team_result);
        resultArea.style.display = 'block';
        
        // 绘制地图
        drawMainMap(mapData);
        
        // 显示无人装备详细信息
        showStationDetail(mapData.stations);
        
        // 显示威胁点详细信息
        showThreatDetail(mapData.threats);
        
        // 显示集中营详细信息
        showCampDetail(mapData.camps);
        
        // 显示集中营分配明细
        showCampAssignDetail(mapData.camp_assignments);
        
        // 显示路径与时间成本
        showPathDetail(mapData.paths, mapData.total_time);
        
        loading.style.display = 'none';
        btn.disabled = false;
    } catch(e) {
        error.textContent = e.message||'网络错误或服务异常';
        error.style.display = 'block';
        loading.style.display = 'none';
        btn.disabled = false;
    }
}

// 显示维修小队结果
function showTeamSummary(data) {
    const teamSummary = document.getElementById('mainTeamSummary');
    teamSummary.innerHTML = `
        <div class="team-card">
            <h4>维修小队1 (A站)</h4>
            <div class="team-count">${data.teams_1}</div>
            <p>需要 ${data.stations_detail.A.count} 个A站</p>
            <p>总消耗: ${data.stations_detail.A.total_cost} 点</p>
        </div>
        <div class="team-card">
            <h4>维修小队2 (B站)</h4>
            <div class="team-count">${data.teams_2}</div>
            <p>需要 ${data.stations_detail.B.count} 个B站</p>
            <p>总消耗: ${data.stations_detail.B.total_cost} 点</p>
        </div>
        <div class="team-card">
            <h4>维修小队3 (C站)</h4>
            <div class="team-count">${data.teams_3}</div>
            <p>需要 ${data.stations_detail.C.count} 个C站</p>
            <p>总消耗: ${data.stations_detail.C.total_cost} 点</p>
        </div>
        <div class="team-card" style="grid-column: 1 / -1; background: linear-gradient(135deg, #ff6b6b, #ee5a24);">
            <h4>总计</h4>
            <div class="team-count">${data.total_teams}</div>
            <p>维修小队总数</p>
        </div>
    `;
}

// 添加重新计算并生成地图的函数
function recalculateAndRedraw() {
    // 显示加载中
    const loading = document.getElementById('mainLoading');
    loading.style.display = 'block';
    
    // 获取当前的数据
    const currentData = window.lastMapData;
    if (!currentData) {
        alert('没有可用的地图数据');
        loading.style.display = 'none';
        return;
    }
    
    // 准备请求数据
    const M = currentData.stations.length;
    const K = currentData.threats.length;
    const P = currentData.camps.length;
    
    // 从当前数据中提取维修站和威胁点的信息
    const stationPositions = currentData.stations.map(s => ({
        id: s.id,
        type: s.type,
        damage_level: s.damage_level,
        x: s.x,
        y: s.y
    }));
    
    const threatPositions = currentData.threats.map(t => ({
        id: t.id,
        range: t.range,
        x: t.x,
        y: t.y
    }));
    
    // 发送请求重新计算
    fetch('/map_data', {
        method: 'POST',
        headers: {'Content-Type':'application/json'},
        body: JSON.stringify({
            M: M,
            K: K,
            P: P,
            stationPositions: stationPositions,
            threatPositions: threatPositions
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        
        // 更新地图数据
        window.lastMapData = data;
        
        // 显示维修小队结果
        showTeamSummary(data.team_result);
        
        // 绘制地图
        drawMainMap(data);
        
        // 显示无人装备详细信息
        showStationDetail(data.stations);
        
        // 显示威胁点详细信息
        showThreatDetail(data.threats);
        
        // 显示集中营详细信息
        showCampDetail(data.camps);
        
        // 显示集中营分配明细
        showCampAssignDetail(data.camp_assignments);
        
        // 显示路径与时间成本
        showPathDetail(data.paths, data.total_time);
        
        // 隐藏加载中
        loading.style.display = 'none';
        
        // 显示结果区域
        document.getElementById('resultArea').style.display = 'block';
    })
    .catch(error => {
        alert('重新计算失败: ' + error.message);
        loading.style.display = 'none';
    });
}

// 页面加载后添加事件监听
document.addEventListener('DOMContentLoaded', function() {
    // 添加计算按钮事件
    document.getElementById('mainBtn').addEventListener('click', calculateAndDraw);
    
    // 添加重新计算按钮事件
    const recalculateBtn = document.getElementById('recalculateBtn');
    if (recalculateBtn) {
        recalculateBtn.addEventListener('click', recalculateAndRedraw);
    }
    
    const recalculateBtn2 = document.getElementById('recalculateBtn2');
    if (recalculateBtn2) {
        recalculateBtn2.addEventListener('click', recalculateAndRedraw);
    }
}); 